#!/usr/bin/env python3
"""
Validation script for Keycloak export JSON file.
"""

import json
import sys
import argparse

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Validate Keycloak export JSON file')
    parser.add_argument('file', help='JSON file to validate')
    return parser.parse_args()

def validate_json_structure(data):
    """Validate the basic JSON structure"""
    required_fields = ['realm', 'enabled', 'groups', 'users']
    
    for field in required_fields:
        if field not in data:
            print(f"✗ Missing required field: {field}")
            return False
    
    print("✓ All required top-level fields present")
    return True

def validate_groups(groups):
    """Validate groups structure"""
    if not isinstance(groups, list):
        print("✗ Groups should be a list")
        return False
    
    required_group_fields = ['name', 'path', 'attributes']
    
    for i, group in enumerate(groups[:5]):  # Check first 5 groups
        for field in required_group_fields:
            if field not in group:
                print(f"✗ Group {i}: Missing field {field}")
                return False
        
        # Check if name matches path
        expected_path = f"/{group['name']}"
        if group['path'] != expected_path:
            print(f"✗ Group {i}: Path mismatch. Expected {expected_path}, got {group['path']}")
            return False
    
    print(f"✓ Groups structure valid (checked {min(5, len(groups))} groups)")
    return True

def validate_users(users):
    """Validate users structure"""
    if not isinstance(users, list):
        print("✗ Users should be a list")
        return False
    
    required_user_fields = ['username', 'enabled', 'email', 'firstName', 'lastName', 'credentials', 'attributes', 'groups']
    
    for i, user in enumerate(users[:5]):  # Check first 5 users
        for field in required_user_fields:
            if field not in user:
                print(f"✗ User {i}: Missing field {field}")
                return False
        
        # Check credentials structure
        if not isinstance(user['credentials'], list) or len(user['credentials']) == 0:
            print(f"✗ User {i}: Invalid credentials structure")
            return False
        
        cred = user['credentials'][0]
        if cred.get('type') != 'password' or not cred.get('value'):
            print(f"✗ User {i}: Invalid credential structure")
            return False
        
        # Check if username is lowercase
        if user['username'] != user['username'].lower():
            print(f"✗ User {i}: Username should be lowercase")
            return False
    
    print(f"✓ Users structure valid (checked {min(5, len(users))} users)")
    return True

def show_statistics(data):
    """Show statistics about the export"""
    print("\n=== Export Statistics ===")
    print(f"Realm: {data['realm']}")
    print(f"Enabled: {data['enabled']}")
    print(f"Total groups: {len(data['groups'])}")
    print(f"Total users: {len(data['users'])}")
    
    # Count users with groups
    users_with_groups = sum(1 for user in data['users'] if user.get('groups'))
    print(f"Users with group assignments: {users_with_groups}")
    
    # Show group distribution
    group_assignments = {}
    for user in data['users']:
        for group in user.get('groups', []):
            group_assignments[group] = group_assignments.get(group, 0) + 1
    
    print(f"Unique groups with user assignments: {len(group_assignments)}")
    
    # Show top 5 groups by user count
    if group_assignments:
        top_groups = sorted(group_assignments.items(), key=lambda x: x[1], reverse=True)[:5]
        print("\nTop 5 groups by user count:")
        for group, count in top_groups:
            print(f"  {group}: {count} users")

def main():
    """Main function"""
    args = parse_args()
    
    print(f"=== Validating {args.file} ===")
    
    try:
        # Load JSON file
        with open(args.file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print("✓ JSON file loaded successfully")
        
        # Validate structure
        if not validate_json_structure(data):
            sys.exit(1)
        
        if not validate_groups(data['groups']):
            sys.exit(1)
        
        if not validate_users(data['users']):
            sys.exit(1)
        
        # Show statistics
        show_statistics(data)
        
        print("\n✓ Validation completed successfully!")
        print("The file is ready for import to Keycloak.")
        
    except json.JSONDecodeError as e:
        print(f"✗ Invalid JSON: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print(f"✗ File not found: {args.file}")
        sys.exit(1)
    except Exception as e:
        print(f"✗ Validation error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
