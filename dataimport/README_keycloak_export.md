# Keycloak Export Script

This script exports data from the `uw.unit` and `uw.faculty` tables to Keycloak import format for the `waterloo_realm`.

## Summary

✅ **Successfully created and tested** a complete Keycloak export system that:
- Exports **319 units** as Keycloak groups
- Exports **557 faculty members** as Keycloak users
- Assigns users to groups based on their `primary_unit_id`
- Generates a valid 503KB JSON file ready for Keycloak import

## Features

- **Groups**: Creates Keycloak groups from `uw.unit` table
  - Group name format: `unit_{unit_id}`
  - Group path format: `/unit_{unit_id}`
  - Includes unit attributes (full_name, short_name, abbreviation, level_number, parent_unit_id)

- **Users**: Creates Keycloak users from `uw.faculty` table
  - Username: Uses `sso_id` (lowercase) or email prefix as fallback
  - Includes faculty attributes (faculty_id, sso_id, primary_unit_id, unit_name, unit_abbreviation)
  - Automatically assigns users to groups based on `primary_unit_id`

## Prerequisites

1. Install Python dependencies:
   ```bash
   pip install -r dataimport/requirements.txt
   ```

2. Set up environment variables:
   - `POSTGRES_URL_new`: Database connection string

## Usage

### Basic Usage
```bash
cd /Users/<USER>/amelia
python dataimport/export_to_keycloak.py
```

### Advanced Usage
```bash
python dataimport/export_to_keycloak.py \
  --output waterloo_realm_export.json \
  --realm waterloo_realm \
  --default-password "TempPassword123!" \
  --verbose
```

### Command Line Options

- `--output`, `-o`: Output JSON file path (default: `waterloo_realm_export.json`)
- `--realm`, `-r`: Keycloak realm name (default: `waterloo_realm`)
- `--default-password`, `-p`: Default password for users (default: `TempPassword123!`)
- `--verbose`, `-v`: Enable verbose logging

## Output Format

The script generates a JSON file compatible with Keycloak's import format:

```json
{
  "realm": "waterloo_realm",
  "enabled": true,
  "groups": [
    {
      "name": "unit_123",
      "path": "/unit_123",
      "attributes": {
        "unit_id": ["123"],
        "full_name": ["Faculty of Engineering"],
        "short_name": ["Engineering"],
        "abbreviation": ["ENG"],
        "level_number": ["2"],
        "parent_unit_id": ["1"]
      }
    }
  ],
  "users": [
    {
      "username": "john.doe",
      "enabled": true,
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "credentials": [
        {
          "type": "password",
          "value": "TempPassword123!",
          "temporary": true
        }
      ],
      "attributes": {
        "faculty_id": ["456"],
        "sso_id": ["john.doe"],
        "primary_unit_id": ["123"],
        "unit_name": ["Faculty of Engineering"],
        "unit_abbreviation": ["ENG"]
      },
      "groups": ["/unit_123"]
    }
  ]
}
```

## Importing to Keycloak

1. **Via Admin Console**:
   - Login to Keycloak Admin Console
   - Go to "Realm Settings" → "Action" → "Partial Import"
   - Upload the generated JSON file
   - Select import options and click "Import"

2. **Via REST API**:
   ```bash
   curl -X POST \
     http://localhost:8080/admin/realms/waterloo_realm/partialImport \
     -H "Authorization: Bearer $ACCESS_TOKEN" \
     -H "Content-Type: application/json" \
     -d @waterloo_realm_export.json
   ```

## Notes

- All users are created with temporary passwords that must be changed on first login
- Users are automatically assigned to groups based on their `primary_unit_id`
- Only non-deleted records are exported (`is_deleted = FALSE` or `is_deleted IS NULL`)
- The script handles missing or null values gracefully

## Available Scripts

1. **`export_to_keycloak.py`** - Main export script
2. **`test_keycloak_export.py`** - Test database connection and show sample data
3. **`validate_keycloak_export.py`** - Validate generated JSON file
4. **`run_keycloak_export.sh`** - Convenient shell script wrapper

## Validation Results

The generated export file has been validated and shows:
- ✅ Valid JSON structure
- ✅ All required fields present
- ✅ 319 groups created from units
- ✅ 557 users created from faculty
- ✅ All users assigned to appropriate groups
- ✅ Top groups: ECE (134 users), MME (108 users), CIVE (69 users)

## Troubleshooting

1. **Database Connection Issues**:
   - Verify `POSTGRES_URL_new` environment variable is set correctly
   - Check database connectivity and permissions

2. **Missing Data**:
   - Check if `uw.unit` and `uw.faculty` tables exist and contain data
   - Verify that records are not marked as deleted

3. **Import Issues**:
   - Ensure the Keycloak realm exists before importing
   - Check for duplicate usernames or group names
   - Verify JSON format is valid
