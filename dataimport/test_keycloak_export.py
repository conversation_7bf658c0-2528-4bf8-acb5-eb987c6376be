#!/usr/bin/env python3
"""
Test script to verify database connection and show sample data for Keycloak export.
"""

import os
import sys
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_URL = os.getenv('POSTGRES_URL_new')

if not DB_URL:
    print("Error: Database connection string is not set. Please set POSTGRES_URL_new environment variable.")
    sys.exit(1)

def test_connection():
    """Test database connection"""
    try:
        conn = psycopg2.connect(DB_URL)
        print("✓ Database connection successful")
        return conn
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        sys.exit(1)

def check_tables(conn):
    """Check if required tables exist"""
    try:
        with conn.cursor() as cur:
            # Check uw.unit table
            cur.execute("""
                SELECT COUNT(*) as count 
                FROM information_schema.tables 
                WHERE table_schema = 'uw' AND table_name = 'unit'
            """)
            unit_exists = cur.fetchone()[0] > 0
            
            # Check uw.faculty table
            cur.execute("""
                SELECT COUNT(*) as count 
                FROM information_schema.tables 
                WHERE table_schema = 'uw' AND table_name = 'faculty'
            """)
            faculty_exists = cur.fetchone()[0] > 0
            
            print(f"✓ uw.unit table exists: {unit_exists}")
            print(f"✓ uw.faculty table exists: {faculty_exists}")
            
            return unit_exists and faculty_exists
    except Exception as e:
        print(f"✗ Error checking tables: {e}")
        return False

def show_sample_data(conn):
    """Show sample data from both tables"""
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Sample units
            print("\n=== Sample Units ===")
            cur.execute("""
                SELECT unit_id, full_name, short_name, abbreviation, level_number, parent_unit_id
                FROM uw.unit 
                WHERE is_deleted = FALSE OR is_deleted IS NULL
                ORDER BY level_number, full_name
                LIMIT 5
            """)
            units = cur.fetchall()
            
            if units:
                for unit in units:
                    print(f"ID: {unit['unit_id']}, Name: {unit['full_name']}, "
                          f"Abbrev: {unit['abbreviation']}, Level: {unit['level_number']}")
            else:
                print("No units found")
            
            # Sample faculty
            print("\n=== Sample Faculty ===")
            cur.execute("""
                SELECT f.faculty_id, f.sso_id, f.first_name, f.last_name, 
                       f.work_email, f.primary_unit_id, u.full_name as unit_name
                FROM uw.faculty f
                LEFT JOIN uw.unit u ON f.primary_unit_id = u.unit_id
                WHERE f.is_deleted = FALSE OR f.is_deleted IS NULL
                ORDER BY f.last_name, f.first_name
                LIMIT 5
            """)
            faculty = cur.fetchall()
            
            if faculty:
                for member in faculty:
                    print(f"ID: {member['faculty_id']}, SSO: {member['sso_id']}, "
                          f"Name: {member['first_name']} {member['last_name']}, "
                          f"Unit: {member['unit_name'] or 'N/A'}")
            else:
                print("No faculty found")
                
    except Exception as e:
        print(f"✗ Error showing sample data: {e}")

def show_statistics(conn):
    """Show data statistics"""
    try:
        with conn.cursor() as cur:
            # Count units
            cur.execute("""
                SELECT COUNT(*) FROM uw.unit 
                WHERE is_deleted = FALSE OR is_deleted IS NULL
            """)
            unit_count = cur.fetchone()[0]
            
            # Count faculty
            cur.execute("""
                SELECT COUNT(*) FROM uw.faculty 
                WHERE is_deleted = FALSE OR is_deleted IS NULL
            """)
            faculty_count = cur.fetchone()[0]
            
            # Count faculty with units
            cur.execute("""
                SELECT COUNT(*) FROM uw.faculty f
                JOIN uw.unit u ON f.primary_unit_id = u.unit_id
                WHERE (f.is_deleted = FALSE OR f.is_deleted IS NULL)
                  AND (u.is_deleted = FALSE OR u.is_deleted IS NULL)
            """)
            faculty_with_units = cur.fetchone()[0]
            
            print("\n=== Data Statistics ===")
            print(f"Total units: {unit_count}")
            print(f"Total faculty: {faculty_count}")
            print(f"Faculty with valid units: {faculty_with_units}")
            print(f"Faculty without units: {faculty_count - faculty_with_units}")
            
    except Exception as e:
        print(f"✗ Error showing statistics: {e}")

def main():
    """Main function"""
    print("=== Keycloak Export Test ===")
    
    # Test connection
    conn = test_connection()
    
    try:
        # Check tables
        if not check_tables(conn):
            print("✗ Required tables not found")
            sys.exit(1)
        
        # Show sample data
        show_sample_data(conn)
        
        # Show statistics
        show_statistics(conn)
        
        print("\n✓ All tests passed! Ready to run Keycloak export.")
        print("\nTo run the export:")
        print("  python dataimport/export_to_keycloak.py")
        print("  # or")
        print("  ./dataimport/run_keycloak_export.sh")
        
    finally:
        conn.close()

if __name__ == "__main__":
    main()
