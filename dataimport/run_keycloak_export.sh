#!/bin/bash

# Keycloak Export Script Runner
# This script runs the Keycloak export with proper environment setup

set -e  # Exit on any error

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "=== Keycloak Export Script ==="
echo "Script directory: $SCRIPT_DIR"
echo "Project root: $PROJECT_ROOT"

# Change to project root
cd "$PROJECT_ROOT"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "Warning: .env file not found in project root"
    echo "Make sure POSTGRES_URL_new environment variable is set"
fi

# Check if requirements are installed
echo "Checking Python dependencies..."
if ! python3 -c "import psycopg2, dotenv" 2>/dev/null; then
    echo "Installing Python dependencies..."
    pip3 install -r dataimport/requirements.txt
fi

# Default values
OUTPUT_FILE="waterloo_realm_export.json"
REALM_NAME="waterloo_realm"
DEFAULT_PASSWORD="TempPassword123!"
VERBOSE=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -o|--output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        -r|--realm)
            REALM_NAME="$2"
            shift 2
            ;;
        -p|--password)
            DEFAULT_PASSWORD="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE="--verbose"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -o, --output FILE     Output JSON file (default: waterloo_realm_export.json)"
            echo "  -r, --realm NAME      Keycloak realm name (default: waterloo_realm)"
            echo "  -p, --password PASS   Default password for users (default: TempPassword123!)"
            echo "  -v, --verbose         Enable verbose logging"
            echo "  -h, --help            Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                                    # Basic export"
            echo "  $0 -o my_export.json -v              # Custom output with verbose logging"
            echo "  $0 -r my_realm -p MyPassword123!     # Custom realm and password"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

echo ""
echo "Export configuration:"
echo "  Output file: $OUTPUT_FILE"
echo "  Realm name: $REALM_NAME"
echo "  Default password: $DEFAULT_PASSWORD"
echo "  Verbose: $([ -n "$VERBOSE" ] && echo "enabled" || echo "disabled")"
echo ""

# Run the export script
echo "Starting Keycloak export..."
python3 dataimport/export_to_keycloak.py \
    --output "$OUTPUT_FILE" \
    --realm "$REALM_NAME" \
    --default-password "$DEFAULT_PASSWORD" \
    $VERBOSE

echo ""
echo "=== Export completed ==="
echo "Output file: $OUTPUT_FILE"

# Show file size and basic stats
if [ -f "$OUTPUT_FILE" ]; then
    FILE_SIZE=$(du -h "$OUTPUT_FILE" | cut -f1)
    echo "File size: $FILE_SIZE"
    
    # Count groups and users in the JSON file
    if command -v jq >/dev/null 2>&1; then
        GROUPS_COUNT=$(jq '.groups | length' "$OUTPUT_FILE" 2>/dev/null || echo "unknown")
        USERS_COUNT=$(jq '.users | length' "$OUTPUT_FILE" 2>/dev/null || echo "unknown")
        echo "Groups: $GROUPS_COUNT"
        echo "Users: $USERS_COUNT"
    fi
fi

echo ""
echo "Next steps:"
echo "1. Review the generated JSON file: $OUTPUT_FILE"
echo "2. Import to Keycloak via Admin Console or REST API"
echo "3. See README_keycloak_export.md for detailed instructions"
