#!/usr/bin/env python3
"""
Script to export data from uw.unit and uw.faculty tables to Keycloak format.
Creates groups from units and users from faculty, with group assignments based on primary_unit_id.
"""

import os
import sys
import json
import argparse
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
from typing import Dict, List, Optional

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('export_to_keycloak')

# Database configuration
DB_URL = os.getenv('POSTGRES_URL_new')

if not DB_URL:
    logger.error("Database connection string is not set. Please set POSTGRES_URL_new environment variable.")
    sys.exit(1)

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Export uw.unit and uw.faculty data to Keycloak format')
    parser.add_argument('--output', '-o', default='waterloo_realm_export.json', 
                       help='Output JSON file path (default: waterloo_realm_export.json)')
    parser.add_argument('--realm', '-r', default='waterloo_realm', 
                       help='Keycloak realm name (default: waterloo_realm)')
    parser.add_argument('--default-password', '-p', default='TempPassword123!', 
                       help='Default password for users (default: TempPassword123!)')
    parser.add_argument('--verbose', '-v', action='store_true', 
                       help='Enable verbose logging')
    return parser.parse_args()

def connect_to_db(connection_string: str) -> psycopg2.extensions.connection:
    """Connect to the database"""
    try:
        conn = psycopg2.connect(connection_string)
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        sys.exit(1)

def fetch_units(conn: psycopg2.extensions.connection) -> List[Dict]:
    """Fetch all units from uw.unit table"""
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT 
                    unit_id,
                    full_name,
                    short_name,
                    abbreviation,
                    parent_unit_id,
                    level_number
                FROM uw.unit 
                WHERE is_deleted = FALSE OR is_deleted IS NULL
                ORDER BY level_number, full_name
            """)
            units = cur.fetchall()
            logger.info(f"Fetched {len(units)} units from database")
            return [dict(unit) for unit in units]
    except Exception as e:
        logger.error(f"Failed to fetch units: {e}")
        return []

def fetch_faculty(conn: psycopg2.extensions.connection) -> List[Dict]:
    """Fetch all faculty from uw.faculty table"""
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT 
                    f.faculty_id,
                    f.sso_id,
                    f.first_name,
                    f.last_name,
                    f.work_email,
                    f.primary_unit_id,
                    u.full_name as unit_name,
                    u.abbreviation as unit_abbreviation
                FROM uw.faculty f
                LEFT JOIN uw.unit u ON f.primary_unit_id = u.unit_id
                WHERE f.is_deleted = FALSE OR f.is_deleted IS NULL
                ORDER BY f.last_name, f.first_name
            """)
            faculty = cur.fetchall()
            logger.info(f"Fetched {len(faculty)} faculty members from database")
            return [dict(member) for member in faculty]
    except Exception as e:
        logger.error(f"Failed to fetch faculty: {e}")
        return []

def create_keycloak_groups(units: List[Dict]) -> List[Dict]:
    """Create Keycloak groups from units"""
    groups = []
    
    for unit in units:
        group = {
            "name": f"unit_{unit['unit_id']}",
            "path": f"/unit_{unit['unit_id']}",
            "attributes": {
                "unit_id": [str(unit['unit_id'])],
                "full_name": [unit['full_name']],
                "short_name": [unit['short_name']],
                "abbreviation": [unit['abbreviation']],
                "level_number": [str(unit['level_number'])]
            }
        }
        
        if unit['parent_unit_id']:
            group["attributes"]["parent_unit_id"] = [str(unit['parent_unit_id'])]
        
        groups.append(group)
    
    logger.info(f"Created {len(groups)} Keycloak groups")
    return groups

def create_keycloak_users(faculty: List[Dict], default_password: str) -> List[Dict]:
    """Create Keycloak users from faculty"""
    users = []
    
    for member in faculty:
        # Use sso_id as username, fallback to work_email if sso_id is not available
        username = member['sso_id'].lower() if member['sso_id'] else member['work_email'].split('@')[0].lower()
        
        user = {
            "username": username,
            "enabled": True,
            "email": member['work_email'],
            "firstName": member['first_name'],
            "lastName": member['last_name'],
            "credentials": [
                {
                    "type": "password",
                    "value": default_password,
                    "temporary": True
                }
            ],
            "attributes": {
                "faculty_id": [str(member['faculty_id'])],
                "sso_id": [member['sso_id']] if member['sso_id'] else [],
                "primary_unit_id": [str(member['primary_unit_id'])] if member['primary_unit_id'] else []
            },
            "groups": []
        }
        
        # Add unit information if available
        if member['unit_name']:
            user["attributes"]["unit_name"] = [member['unit_name']]
        if member['unit_abbreviation']:
            user["attributes"]["unit_abbreviation"] = [member['unit_abbreviation']]
        
        # Assign user to group based on primary_unit_id
        if member['primary_unit_id']:
            user["groups"].append(f"/unit_{member['primary_unit_id']}")
        
        users.append(user)
    
    logger.info(f"Created {len(users)} Keycloak users")
    return users

def create_keycloak_export(realm_name: str, groups: List[Dict], users: List[Dict]) -> Dict:
    """Create the complete Keycloak export structure"""
    export_data = {
        "realm": realm_name,
        "enabled": True,
        "groups": groups,
        "users": users
    }
    
    return export_data

def main():
    """Main function"""
    args = parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info(f"Starting Keycloak export for realm: {args.realm}")
    
    # Connect to database
    conn = connect_to_db(DB_URL)
    
    try:
        # Fetch data from database
        units = fetch_units(conn)
        faculty = fetch_faculty(conn)
        
        if not units:
            logger.warning("No units found in database")
        if not faculty:
            logger.warning("No faculty found in database")
        
        # Create Keycloak structures
        groups = create_keycloak_groups(units)
        users = create_keycloak_users(faculty, args.default_password)
        
        # Create export data
        export_data = create_keycloak_export(args.realm, groups, users)
        
        # Write to file
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Export completed successfully!")
        logger.info(f"Output file: {args.output}")
        logger.info(f"Realm: {args.realm}")
        logger.info(f"Groups created: {len(groups)}")
        logger.info(f"Users created: {len(users)}")
        
    except Exception as e:
        logger.error(f"Export failed: {e}")
        sys.exit(1)
    finally:
        conn.close()

if __name__ == "__main__":
    main()
