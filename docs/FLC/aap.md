# Appointment Approval Process
#### This is the workflow to process the approval of faculty appointments, and feed information to HRIS (Workday in the case of UW).

## Types of Faculty Appointments
- Primary Appointments
- Secondary Appointments

## Output
- for Primary Appointments
  1. [Full Time Faculty Appointment Form](https://github.com/user-attachments/files/20747277/full-time_faculty_appointment_form_0_1.pdf)

  2. Letter of Offer issued by Dean
  3. Employment Agreement (subject to MOA)
- for Secondary Appointments
  1. Terms of Appointment
  2. Appointment Forms
     - Administrative Appointment Form
     - [Research Chair Appointment Form](https://github.com/user-attachments/files/20762607/research_chair_appointment_form.pdf)

 
## System Roles
- department_support: data entry
- department_admin: check to ensure department_support has done their job
- department_approver: approve the appointment on behalf of the unit (department/school)
- faculty_support: review of appointment form; and edit the draft offer letter that the system generates
- faculty_admin: review work done by faculty_support, and approve
- faculty_approver: approve the appointment
- division_support
- division_admin
- division_approver: Provost approval

## Workflow: Primary Appointments
1. After a faculty recruitment process is completed (i.e. after the approval of UARC), the AAP process is activated;
2. Employee Information (including education background) would be automatically pre-populated;
3. Some of the Appointment Information would be automatically pre-populated; remaining information to be completed by department_support;
4. department_support also completes or verifies Financial Information, and in "Other Remarks", notes any special terms about the offer;
5. Once department_support completes their steps, the approval flow starts.
6. When the process reaches faculty_support, the system would generate an offer package (containing the offer letter and employment agreement) for review by faculty_support.

### Return Logic
- The system should follow default return logic however it would be helpful to allow users to change the logic if it is necessary.
- Default return logic:
  - If department_admin, department_approver or faculty_support rejects, it goes to department_support;
  - If faculty_admin, faculty_approver or division_support rejects, it goes to faculty_support;
  - Very rarely, if division_admin or division_approver rejects, it goes to faculty_support.

## Workflow: Secondary Appointments
1. faculty_support initiates a secondary appointment;
2. For **Terms of Appointment**:
  - draft terms of appointment is automatically generated based on templates and appointment title chosen;
  - faculty_support reviews the draft and edits the terms accordingly;
3. For **Secondary Appointment Form** (i.e. Administrative Appointment Form):
  - personal information would be automatically pre-polulated;
  - faculty_support completes the remaining information;
4. Once faculty_support completes their work, the approval process starts.
  - Approval path depends on the appointment title. For Associate Dean appointment, it does not require Chair/Director signature;
  - Typically approval path is: faculty_admin --> faculty_approver --> appointee (only approves terms of appointment) --> division_support --> division_admin --> division_approver;
  - The appointee only signs off on the terms of appointment.











