# Faculty Recruitment Module
##### May 24, 2025
##### Written on flight from Toronto to Beijing

#### The Faculty Recruitment is a foundational module of Foresight, Intelicampus FLIS (Faculty Lifecycle Information System).
#### Existing homegrown system at UW: [Online Faculty Application System](https://ofas.uwaterloo.ca)

## UW Policies (for reference)
- [Policy 76: Faculty Appointments](https://uwaterloo.ca/secretariat/policies-procedures-guidelines/policy-76#:~:text=Faculty%20appointments%20can%20be%20made,of%20the%20Board%20of%20Governors.)
- [Policy 40: The Chair](https://uwaterloo.ca/secretariat/policies-procedures-guidelines/policy-40#:~:text=Policy%2040%20provides%20that%20the,tenured%20faculty%20member%20at%20UW.)
- [Policy 45: The Dean of a Faculty](https://uwaterloo.ca/secretariat/policies-procedures-guidelines/policy-40#:~:text=Policy%2040%20provides%20that%20the,tenured%20faculty%20member%20at%20UW.)

## Roles
1. Applicants (mostly external, and possibility of internal candidates)
2. Hiring Committee: typically DACA/SACA; rarely Chair/Director Nominating Committee, or Dean Nominating Committee
3. Hiring Committee administrator who is appointed to act on behalf of the Hiring Committee. For DACA/SACA, this role is typically served by Assistant to Department Chair / School Director. The Chair of the Hiring Committee should be able to specify/change who the Administrator is.
4. Approver bodies
    1. Chair/Director, if applicable
    2. Dean
    3. University Appointments Review Committee ([UARC](https://uwaterloo.ca/faculty-planning-policy/recruitment/university-appointments-review-committee))
    5. Provost
    6. President
    7. Board of Governors

## Type of Positions
1. Regular faculty positions
2. Combination of regular faculty position and an administrative appointment. This would trigger the secondary appointment process.

## Key Functionality
1. Candidate to
    1. Create a Intelicampus profile, if it does not exist
    2. Prepare their application in accordance with requirements specified
    3. Upload supporting materials
    5. Receive status update
3. Hiring Committee to 
    1. define application materials required;
    2. Define attributes / evaluation rubric required;
    3. Evaluate candidates by committee members (system to record both scores and comments);
    4. Develop a long short-list;
    5. From the long short-list, narrow down to a short-list;
    6. Ability for committee members to vote (secrete votes) to select a final candidate.
    7. System to draft a UARC memo, along with supporting documentation.
  
## Output
- Consolidated application package for each candidate
- [Summary of recruitment efforts](https://github.com/user-attachments/files/20745431/summary-of-recruiting-efforts_1_0_1_0.pdf)
- [Chair's Memo on behalf of DACA/SACA](https://uwaterloo.ca/secretariat/chairs-memo-dean-re-summary-recruiting-efforts)

## Materials Required from Candidates
- Typically an application requires a cover letter; statements of research, teaching and service; a CV, samples of publication or scholarly output, names of reference
- In terms of reference,
    - for regular faculty searches, the process typically requires the reference letters before the Committee would review applications;
    - for senior faculty appointments, typically letters of reference are collected toward the end of the process.

## Work Flow
1. Recruitment process starts after mission critical form and job advertisement are approved (done on Position Control and Complement Planning module);
2. Approved advertisement is published;
3. Hiring Committee administrator to set or configure 
    1. Deadlines (e.g. application deadline, deadline for Committee members to evaluate applications, etc.)
    2. If the standard application requirements should be modified.
    3. If the stadnard rubric should be modified.
    4. How applications will be reviewed: by every Committee member or divide-and-conquer
4. Committee members receive regular updates (e.g. weekly) on applications received.
5. Committee members may review applications as they are received.
6. Committee members may adjust their scores/comments.
7. The system keeps a complete list of applications received.
8. Following Committee deliberations, the system generates a long short-list, from which 5-6 candidates may be invited to a preliminary interview.
9. Based on the preliminary interview and committee deliberations, a short-list of 3-4 candidates will be developed; and they will be invited to an on-campus interview.
10. Following the on-campus interviews, the successful candidate(s) will be identified. There is possibility more than one candidate will be identified.
11. The system put together required documentation for review by the Dean's Office.
12. Dean's Office reviews and submits the documentation to UARC for approval. UARC approves (or requests clarification), and the approval is returned to the Dean's Office, with copy to the Hiring Committee.
13. The Hiring Unit initiates the appointment process (which is a separate Intelicampus module). 
