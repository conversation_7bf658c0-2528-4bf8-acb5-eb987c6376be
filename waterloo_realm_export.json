{"realm": "waterloo_realm", "enabled": true, "groups": [{"name": "unit_100", "path": "/unit_100", "attributes": {"unit_id": ["100"], "full_name": ["University of Waterloo"], "short_name": ["UWaterloo"], "abbreviation": ["UW"], "level_number": ["1"]}}, {"name": "unit_398", "path": "/unit_398", "attributes": {"unit_id": ["398"], "full_name": ["Affiliated and Federated Institutions of Waterloo"], "short_name": ["AFIW"], "abbreviation": ["AFIW"], "level_number": ["2"], "parent_unit_id": ["100"]}}, {"name": "unit_350", "path": "/unit_350", "attributes": {"unit_id": ["350"], "full_name": ["Vice-President Academic"], "short_name": ["Academic Division"], "abbreviation": ["VPA"], "level_number": ["2"], "parent_unit_id": ["100"]}}, {"name": "unit_358", "path": "/unit_358", "attributes": {"unit_id": ["358"], "full_name": ["Faculty of Arts"], "short_name": ["Arts"], "abbreviation": ["ARTS"], "level_number": ["3"], "parent_unit_id": ["350"]}}, {"name": "unit_359", "path": "/unit_359", "attributes": {"unit_id": ["359"], "full_name": ["Faculty of Engineering"], "short_name": ["Engineering"], "abbreviation": ["ENG"], "level_number": ["3"], "parent_unit_id": ["350"]}}, {"name": "unit_360", "path": "/unit_360", "attributes": {"unit_id": ["360"], "full_name": ["Faculty of Environment"], "short_name": ["Environment"], "abbreviation": ["ENV"], "level_number": ["3"], "parent_unit_id": ["350"]}}, {"name": "unit_357", "path": "/unit_357", "attributes": {"unit_id": ["357"], "full_name": ["Faculty of Health"], "short_name": ["Health"], "abbreviation": ["HEALTH"], "level_number": ["3"], "parent_unit_id": ["350"]}}, {"name": "unit_361", "path": "/unit_361", "attributes": {"unit_id": ["361"], "full_name": ["Faculty of Mathematics"], "short_name": ["Mathematics"], "abbreviation": ["MATH"], "level_number": ["3"], "parent_unit_id": ["350"]}}, {"name": "unit_362", "path": "/unit_362", "attributes": {"unit_id": ["362"], "full_name": ["Faculty of Science"], "short_name": ["Science"], "abbreviation": ["SCI"], "level_number": ["3"], "parent_unit_id": ["350"]}}, {"name": "unit_4000", "path": "/unit_4000", "attributes": {"unit_id": ["4000"], "full_name": ["Conrad School of Entrepreneurship and Business"], "short_name": ["Conrad School"], "abbreviation": ["CONRAD"], "level_number": ["4"], "parent_unit_id": ["359"]}}, {"name": "unit_2175", "path": "/unit_2175", "attributes": {"unit_id": ["2175"], "full_name": ["Dean's Office, Faculty of Engineering"], "short_name": ["Dean of Engineering Office"], "abbreviation": ["DOE"], "level_number": ["4"], "parent_unit_id": ["359"]}}, {"name": "unit_2100", "path": "/unit_2100", "attributes": {"unit_id": ["2100"], "full_name": ["Department of Chemical Engineering"], "short_name": ["Chemical Engineering"], "abbreviation": ["CHE"], "level_number": ["4"], "parent_unit_id": ["359"]}}, {"name": "unit_2125", "path": "/unit_2125", "attributes": {"unit_id": ["2125"], "full_name": ["Department of Civil and Environmental Engineering"], "short_name": ["Civil & Environmental Engineering"], "abbreviation": ["CEE"], "level_number": ["4"], "parent_unit_id": ["359"]}}, {"name": "unit_2200", "path": "/unit_2200", "attributes": {"unit_id": ["2200"], "full_name": ["Department of Electrical and Computer Engineering"], "short_name": ["Electrical and Computer Engineering"], "abbreviation": ["ECE"], "level_number": ["4"], "parent_unit_id": ["359"]}}, {"name": "unit_2400", "path": "/unit_2400", "attributes": {"unit_id": ["2400"], "full_name": ["Department of Management Science and Engineering"], "short_name": ["Management Science and Engineering"], "abbreviation": ["MSE"], "level_number": ["4"], "parent_unit_id": ["359"]}}, {"name": "unit_2425", "path": "/unit_2425", "attributes": {"unit_id": ["2425"], "full_name": ["Department of Mechanical and Mechatronics Engineering"], "short_name": ["Mechanical and Mechatronics Engineering"], "abbreviation": ["MME"], "level_number": ["4"], "parent_unit_id": ["359"]}}, {"name": "unit_2450", "path": "/unit_2450", "attributes": {"unit_id": ["2450"], "full_name": ["Department of Systems Design Engineering"], "short_name": ["Systems"], "abbreviation": ["SDE"], "level_number": ["4"], "parent_unit_id": ["359"]}}, {"name": "unit_2600", "path": "/unit_2600", "attributes": {"unit_id": ["2600"], "full_name": ["School of Architecture"], "short_name": ["Architecture"], "abbreviation": ["ARCH"], "level_number": ["4"], "parent_unit_id": ["359"]}}, {"name": "unit_1975", "path": "/unit_1975", "attributes": {"unit_id": ["1975"], "full_name": ["Stratford School of Interaction Design and Business"], "short_name": ["Stratford"], "abbreviation": ["STRATFORD"], "level_number": ["4"], "parent_unit_id": ["358"]}}, {"name": "unit_2177", "path": "/unit_2177", "attributes": {"unit_id": ["2177"], "full_name": ["Eng Deans Office-Outreach"], "short_name": ["Eng Deans Office-Outreach"], "abbreviation": [null], "level_number": ["5"]}}, {"name": "unit_2275", "path": "/unit_2275", "attributes": {"unit_id": ["2275"], "full_name": ["Eng-Computing Office"], "short_name": ["Eng-Computing Office"], "abbreviation": [null], "level_number": ["5"]}}, {"name": "unit_2179", "path": "/unit_2179", "attributes": {"unit_id": ["2179"], "full_name": ["Eng-Core Facilities"], "short_name": ["Eng-Core Facilities"], "abbreviation": [null], "level_number": ["5"]}}, {"name": "unit_2176", "path": "/unit_2176", "attributes": {"unit_id": ["2176"], "full_name": ["Eng-Development & Alumni"], "short_name": ["Eng-Development & Alumni"], "abbreviation": [null], "level_number": ["5"]}}, {"name": "unit_2225", "path": "/unit_2225", "attributes": {"unit_id": ["2225"], "full_name": ["Eng-Eng Machine Shop"], "short_name": ["Eng-Eng Machine Shop"], "abbreviation": [null], "level_number": ["5"]}}, {"name": "unit_2310", "path": "/unit_2310", "attributes": {"unit_id": ["2310"], "full_name": ["Eng-Graduate Studies Office"], "short_name": ["Eng-Graduate Studies Office"], "abbreviation": [null], "level_number": ["5"]}}, {"name": "unit_2227", "path": "/unit_2227", "attributes": {"unit_id": ["2227"], "full_name": ["Eng-Machine Shop-Student Shop"], "short_name": ["Eng-Machine Shop-Student Shop"], "abbreviation": [null], "level_number": ["5"]}}, {"name": "unit_2178", "path": "/unit_2178", "attributes": {"unit_id": ["2178"], "full_name": ["Eng-Student Design Centre"], "short_name": ["Eng-Student Design Centre"], "abbreviation": [null], "level_number": ["5"]}}, {"name": "unit_2300", "path": "/unit_2300", "attributes": {"unit_id": ["2300"], "full_name": ["Eng-Undergrad Studies Office"], "short_name": ["Eng-Undergrad Studies Office"], "abbreviation": [null], "level_number": ["5"]}}, {"name": "unit_5435", "path": "/unit_5435", "attributes": {"unit_id": ["5435"], "full_name": ["AP Campus Support-AccessAbility Services"], "short_name": ["AP Campus Support-AccessAbility Services"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5551", "path": "/unit_5551", "attributes": {"unit_id": ["5551"], "full_name": ["AP Campus Support-Conflict Mgmt & Human Rights"], "short_name": ["AP Campus Support-Conflict Mgmt & Human Rights"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5751", "path": "/unit_5751", "attributes": {"unit_id": ["5751"], "full_name": ["AP Campus Support-Disability Inclusion"], "short_name": ["AP Campus Support-Disability Inclusion"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5325", "path": "/unit_5325", "attributes": {"unit_id": ["5325"], "full_name": ["AP Campus Support-Occupational Hlth"], "short_name": ["AP Campus Support-Occupational Hlth"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5437", "path": "/unit_5437", "attributes": {"unit_id": ["5437"], "full_name": ["AP Campus Support-Sexual Violence Prevention & Response Office"], "short_name": ["AP Campus Support-Sexual Violence Prevention & Response Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5430", "path": "/unit_5430", "attributes": {"unit_id": ["5430"], "full_name": ["AP Students Office"], "short_name": ["AP Students Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5240", "path": "/unit_5240", "attributes": {"unit_id": ["5240"], "full_name": ["AVP Academic Operations"], "short_name": ["AVP Academic Operations"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5255", "path": "/unit_5255", "attributes": {"unit_id": ["5255"], "full_name": ["AVP Advancement Strategy"], "short_name": ["AVP Advancement Strategy"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5269", "path": "/unit_5269", "attributes": {"unit_id": ["5269"], "full_name": ["AVP Development Central Development"], "short_name": ["AVP Development Central Development"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5360", "path": "/unit_5360", "attributes": {"unit_id": ["5360"], "full_name": ["AVP EDI-R"], "short_name": ["AVP EDI-R"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5259", "path": "/unit_5259", "attributes": {"unit_id": ["5259"], "full_name": ["AVP Engagement & Leadership Giving"], "short_name": ["AVP Engagement & Leadership Giving"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5110", "path": "/unit_5110", "attributes": {"unit_id": ["5110"], "full_name": ["AVP Engagement Alumni Relations"], "short_name": ["AVP Engagement Alumni Relations"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5268", "path": "/unit_5268", "attributes": {"unit_id": ["5268"], "full_name": ["AVP Engagement Annual Giving"], "short_name": ["AVP Engagement Annual Giving"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5267", "path": "/unit_5267", "attributes": {"unit_id": ["5267"], "full_name": ["AVP Engagement Planned Giving"], "short_name": ["AVP Engagement Planned Giving"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5410", "path": "/unit_5410", "attributes": {"unit_id": ["5410"], "full_name": ["AVP Faculty Planning & Policy"], "short_name": ["AVP Faculty Planning & Policy"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5310", "path": "/unit_5310", "attributes": {"unit_id": ["5310"], "full_name": ["AVP GSPA Office"], "short_name": ["AVP GSPA Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5130", "path": "/unit_5130", "attributes": {"unit_id": ["5130"], "full_name": ["AVP Global Futures"], "short_name": ["AVP Global Futures"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5720", "path": "/unit_5720", "attributes": {"unit_id": ["5720"], "full_name": ["AVP Hum Right, Equity & Inclusion"], "short_name": ["AVP Hum Right, Equity & Inclusion"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1300", "path": "/unit_1300", "attributes": {"unit_id": ["1300"], "full_name": ["Accounting & Finance"], "short_name": ["Accounting & Finance"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1302", "path": "/unit_1302", "attributes": {"unit_id": ["1302"], "full_name": ["Accounting & Finance-MAcc"], "short_name": ["Accounting & Finance-MAcc"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1301", "path": "/unit_1301", "attributes": {"unit_id": ["1301"], "full_name": ["Accounting & Finance-MTax"], "short_name": ["Accounting & Finance-MTax"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1305", "path": "/unit_1305", "attributes": {"unit_id": ["1305"], "full_name": ["Accounting & Finance-SFMP"], "short_name": ["Accounting & Finance-SFMP"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1304", "path": "/unit_1304", "attributes": {"unit_id": ["1304"], "full_name": ["Accounting & Financial Mgmt"], "short_name": ["Accounting & Financial Mgmt"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5251", "path": "/unit_5251", "attributes": {"unit_id": ["5251"], "full_name": ["Adv Serv AVP"], "short_name": ["Adv Serv AVP"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5266", "path": "/unit_5266", "attributes": {"unit_id": ["5266"], "full_name": ["Adv Serv Donor Relations"], "short_name": ["Adv Serv Donor Relations"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5250", "path": "/unit_5250", "attributes": {"unit_id": ["5250"], "full_name": ["Adv Serv Operations"], "short_name": ["Adv Serv Operations"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5253", "path": "/unit_5253", "attributes": {"unit_id": ["5253"], "full_name": ["Adv Serv Prospect Research"], "short_name": ["Adv Serv Prospect Research"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5254", "path": "/unit_5254", "attributes": {"unit_id": ["5254"], "full_name": ["Adv Serv Systems"], "short_name": ["Adv Serv Systems"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5256", "path": "/unit_5256", "attributes": {"unit_id": ["5256"], "full_name": ["Advancement Serv Campaign General"], "short_name": ["Advancement Serv Campaign General"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5260", "path": "/unit_5260", "attributes": {"unit_id": ["5260"], "full_name": ["Advancement Serv Faculty Advancement"], "short_name": ["Advancement Serv Faculty Advancement"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1325", "path": "/unit_1325", "attributes": {"unit_id": ["1325"], "full_name": ["Anthropology"], "short_name": ["Anthropology"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2900", "path": "/unit_2900", "attributes": {"unit_id": ["2900"], "full_name": ["Applied Mathematics"], "short_name": ["Applied Mathematics"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1551", "path": "/unit_1551", "attributes": {"unit_id": ["1551"], "full_name": ["Arts-Advancement"], "short_name": ["Arts-Advancement"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1400", "path": "/unit_1400", "attributes": {"unit_id": ["1400"], "full_name": ["Arts-Computing Office"], "short_name": ["Arts-Computing Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1550", "path": "/unit_1550", "attributes": {"unit_id": ["1550"], "full_name": ["Arts-Deans Office"], "short_name": ["Arts-Deans Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1375", "path": "/unit_1375", "attributes": {"unit_id": ["1375"], "full_name": ["Arts-General"], "short_name": ["Arts-General"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1425", "path": "/unit_1425", "attributes": {"unit_id": ["1425"], "full_name": ["Arts-Grad Office"], "short_name": ["Arts-Grad Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1500", "path": "/unit_1500", "attributes": {"unit_id": ["1500"], "full_name": ["Arts-Undergrad Office"], "short_name": ["Arts-Undergrad Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5490", "path": "/unit_5490", "attributes": {"unit_id": ["5490"], "full_name": ["Assoc VP Acad Office"], "short_name": ["Assoc VP Acad Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5750", "path": "/unit_5750", "attributes": {"unit_id": ["5750"], "full_name": ["Associate Provost, Campus Support & Accessibility"], "short_name": ["Associate Provost, Campus Support & Accessibility"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5125", "path": "/unit_5125", "attributes": {"unit_id": ["5125"], "full_name": ["Ath & Rec-Facilities"], "short_name": ["Ath & Rec-Facilities"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5122", "path": "/unit_5122", "attributes": {"unit_id": ["5122"], "full_name": ["Ath & Rec-Interuniv"], "short_name": ["Ath & Rec-Interuniv"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5123", "path": "/unit_5123", "attributes": {"unit_id": ["5123"], "full_name": ["Ath & Rec-Interuniv Teams"], "short_name": ["Ath & Rec-Interuniv Teams"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5124", "path": "/unit_5124", "attributes": {"unit_id": ["5124"], "full_name": ["Ath & Rec-Marketing"], "short_name": ["Ath & Rec-Marketing"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5126", "path": "/unit_5126", "attributes": {"unit_id": ["5126"], "full_name": ["Ath & Rec-Recreation Clubs"], "short_name": ["Ath & Rec-Recreation Clubs"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5121", "path": "/unit_5121", "attributes": {"unit_id": ["5121"], "full_name": ["Ath & Rec-Recreation Prg"], "short_name": ["Ath & Rec-Recreation Prg"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5127", "path": "/unit_5127", "attributes": {"unit_id": ["5127"], "full_name": ["Ath & Rec-Recreation Serv"], "short_name": ["Ath & Rec-Recreation Serv"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5120", "path": "/unit_5120", "attributes": {"unit_id": ["5120"], "full_name": ["Athletics & Recreation-Admin"], "short_name": ["Athletics & Recreation-Admin"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3300", "path": "/unit_3300", "attributes": {"unit_id": ["3300"], "full_name": ["Biology"], "short_name": ["Biology"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2552", "path": "/unit_2552", "attributes": {"unit_id": ["2552"], "full_name": ["Biomedical UG Engineering"], "short_name": ["Biomedical UG Engineering"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_8069", "path": "/unit_8069", "attributes": {"unit_id": ["8069"], "full_name": ["CARE"], "short_name": ["CARE"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_8065", "path": "/unit_8065", "attributes": {"unit_id": ["8065"], "full_name": ["CARE-CAE"], "short_name": ["CARE-CAE"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5190", "path": "/unit_5190", "attributes": {"unit_id": ["5190"], "full_name": ["CEE"], "short_name": ["CEE"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_8073", "path": "/unit_8073", "attributes": {"unit_id": ["8073"], "full_name": ["CORE"], "short_name": ["CORE"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5700", "path": "/unit_5700", "attributes": {"unit_id": ["5700"], "full_name": ["Campus Wellness Administration"], "short_name": ["Campus Wellness Administration"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5160", "path": "/unit_5160", "attributes": {"unit_id": ["5160"], "full_name": ["Central Stores"], "short_name": ["Central Stores"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5182", "path": "/unit_5182", "attributes": {"unit_id": ["5182"], "full_name": ["Centre for Career Development"], "short_name": ["Centre for Career Development"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5270", "path": "/unit_5270", "attributes": {"unit_id": ["5270"], "full_name": ["Centre for Extended Learning"], "short_name": ["Centre for Extended Learning"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5620", "path": "/unit_5620", "attributes": {"unit_id": ["5620"], "full_name": ["Centre for Teaching Excellence"], "short_name": ["Centre for Teaching Excellence"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5340", "path": "/unit_5340", "attributes": {"unit_id": ["5340"], "full_name": ["Centre for WIL"], "short_name": ["Centre for WIL"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3325", "path": "/unit_3325", "attributes": {"unit_id": ["3325"], "full_name": ["Chemistry"], "short_name": ["Chemistry"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1525", "path": "/unit_1525", "attributes": {"unit_id": ["1525"], "full_name": ["Classical Studies"], "short_name": ["Classical Studies"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5180", "path": "/unit_5180", "attributes": {"unit_id": ["5180"], "full_name": ["Co-op Education"], "short_name": ["Co-op Education"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2925", "path": "/unit_2925", "attributes": {"unit_id": ["2925"], "full_name": ["Combinatorics & Optimization"], "short_name": ["Combinatorics & Optimization"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1575", "path": "/unit_1575", "attributes": {"unit_id": ["1575"], "full_name": ["Communication Arts"], "short_name": ["Communication Arts"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1577", "path": "/unit_1577", "attributes": {"unit_id": ["1577"], "full_name": ["Communication Arts-CCAT"], "short_name": ["Communication Arts-CCAT"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1576", "path": "/unit_1576", "attributes": {"unit_id": ["1576"], "full_name": ["Communication Arts-ML Theatre"], "short_name": ["Communication Arts-ML Theatre"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2950", "path": "/unit_2950", "attributes": {"unit_id": ["2950"], "full_name": ["Computer Science"], "short_name": ["Computer Science"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2951", "path": "/unit_2951", "attributes": {"unit_id": ["2951"], "full_name": ["Computer Science-CSCF"], "short_name": ["Computer Science-CSCF"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_7100", "path": "/unit_7100", "attributes": {"unit_id": ["7100"], "full_name": ["<PERSON> Univ College"], "short_name": ["<PERSON> Univ College"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5230", "path": "/unit_5230", "attributes": {"unit_id": ["5230"], "full_name": ["Counselling"], "short_name": ["Counselling"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2490", "path": "/unit_2490", "attributes": {"unit_id": ["2490"], "full_name": ["Ctr for Bioeng & Biotech"], "short_name": ["Ctr for Bioeng & Biotech"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3003", "path": "/unit_3003", "attributes": {"unit_id": ["3003"], "full_name": ["Ctr for Educ in Math & Comp"], "short_name": ["Ctr for Educ in Math & Comp"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_8088", "path": "/unit_8088", "attributes": {"unit_id": ["8088"], "full_name": ["Ctr for Sight Enhancement"], "short_name": ["Ctr for Sight Enhancement"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5479", "path": "/unit_5479", "attributes": {"unit_id": ["5479"], "full_name": ["Cybersecurity & Privacy Institute"], "short_name": ["Cybersecurity & Privacy Institute"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3375", "path": "/unit_3375", "attributes": {"unit_id": ["3375"], "full_name": ["Earth & Environmental Sciences"], "short_name": ["Earth & Environmental Sciences"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1600", "path": "/unit_1600", "attributes": {"unit_id": ["1600"], "full_name": ["Economics"], "short_name": ["Economics"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1625", "path": "/unit_1625", "attributes": {"unit_id": ["1625"], "full_name": ["English Lang & Literature"], "short_name": ["English Lang & Literature"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2700", "path": "/unit_2700", "attributes": {"unit_id": ["2700"], "full_name": ["Env, Res & Sustainability"], "short_name": ["Env, Res & Sustainability"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2627", "path": "/unit_2627", "attributes": {"unit_id": ["2627"], "full_name": ["Env-Economic Development"], "short_name": ["Env-Economic Development"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2751", "path": "/unit_2751", "attributes": {"unit_id": ["2751"], "full_name": ["Env-WISIR"], "short_name": ["Env-WISIR"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2725", "path": "/unit_2725", "attributes": {"unit_id": ["2725"], "full_name": ["Environment Technology & Instructional Support"], "short_name": ["Environment Technology & Instructional Support"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2625", "path": "/unit_2625", "attributes": {"unit_id": ["2625"], "full_name": ["Environment-Deans Office"], "short_name": ["Environment-Deans Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5910", "path": "/unit_5910", "attributes": {"unit_id": ["5910"], "full_name": ["Fin-YE Accrual-Acad & Research"], "short_name": ["Fin-YE Accrual-Acad & Research"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5911", "path": "/unit_5911", "attributes": {"unit_id": ["5911"], "full_name": ["Fin-YE Accrual-Acad Supp & Ancill"], "short_name": ["Fin-YE Accrual-Acad Supp & Ancill"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5290", "path": "/unit_5290", "attributes": {"unit_id": ["5290"], "full_name": ["Finance"], "short_name": ["Finance"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5907", "path": "/unit_5907", "attributes": {"unit_id": ["5907"], "full_name": ["Finance-Asset Clearing"], "short_name": ["Finance-Asset Clearing"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5906", "path": "/unit_5906", "attributes": {"unit_id": ["5906"], "full_name": ["Finance-Budget Reporting"], "short_name": ["Finance-Budget Reporting"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5900", "path": "/unit_5900", "attributes": {"unit_id": ["5900"], "full_name": ["Finance-Gen & Adm"], "short_name": ["Finance-Gen & Adm"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5904", "path": "/unit_5904", "attributes": {"unit_id": ["5904"], "full_name": ["Finance-Leases & Rentals"], "short_name": ["Finance-Leases & Rentals"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1650", "path": "/unit_1650", "attributes": {"unit_id": ["1650"], "full_name": ["Fine Arts"], "short_name": ["Fine Arts"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1651", "path": "/unit_1651", "attributes": {"unit_id": ["1651"], "full_name": ["Fine Arts-ML Gallery"], "short_name": ["Fine Arts-ML Gallery"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6222", "path": "/unit_6222", "attributes": {"unit_id": ["6222"], "full_name": ["Food Serv-Hagey Hall Convenience"], "short_name": ["Food Serv-Hagey Hall Convenience"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6220", "path": "/unit_6220", "attributes": {"unit_id": ["6220"], "full_name": ["Food Serv-South Side Marketplace"], "short_name": ["Food Serv-South Side Marketplace"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6228", "path": "/unit_6228", "attributes": {"unit_id": ["6228"], "full_name": ["Food Serv-Tim Hortons Express #5364 DC"], "short_name": ["Food Serv-Tim Hortons Express #5364 DC"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6219", "path": "/unit_6219", "attributes": {"unit_id": ["6219"], "full_name": ["Food Serv-Williams Fresh Café"], "short_name": ["Food Serv-Williams Fresh Café"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6200", "path": "/unit_6200", "attributes": {"unit_id": ["6200"], "full_name": ["Food Services"], "short_name": ["Food Services"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6203", "path": "/unit_6203", "attributes": {"unit_id": ["6203"], "full_name": ["Food Services-Arts Coffee Shop"], "short_name": ["Food Services-Arts Coffee Shop"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6206", "path": "/unit_6206", "attributes": {"unit_id": ["6206"], "full_name": ["Food Services-BMH"], "short_name": ["Food Services-BMH"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6209", "path": "/unit_6209", "attributes": {"unit_id": ["6209"], "full_name": ["Food Services-<PERSON> (DC)"], "short_name": ["Food Services-<PERSON> (DC)"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6211", "path": "/unit_6211", "attributes": {"unit_id": ["6211"], "full_name": ["Food Services-Browsers Cafe"], "short_name": ["Food Services-Browsers Cafe"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6212", "path": "/unit_6212", "attributes": {"unit_id": ["6212"], "full_name": ["Food Services-Brubakers"], "short_name": ["Food Services-Brubakers"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6223", "path": "/unit_6223", "attributes": {"unit_id": ["6223"], "full_name": ["Food Services-CEIT Cafe"], "short_name": ["Food Services-CEIT Cafe"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6227", "path": "/unit_6227", "attributes": {"unit_id": ["6227"], "full_name": ["Food Services-Catering & Events"], "short_name": ["Food Services-Catering & Events"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6208", "path": "/unit_6208", "attributes": {"unit_id": ["6208"], "full_name": ["Food Services-Chatime"], "short_name": ["Food Services-Chatime"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6210", "path": "/unit_6210", "attributes": {"unit_id": ["6210"], "full_name": ["Food Services-EC5 Coffee"], "short_name": ["Food Services-EC5 Coffee"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6233", "path": "/unit_6233", "attributes": {"unit_id": ["6233"], "full_name": ["Food Services-Eye Opener Café"], "short_name": ["Food Services-Eye Opener Café"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6221", "path": "/unit_6221", "attributes": {"unit_id": ["6221"], "full_name": ["Food Services-Jugo Juice CIF"], "short_name": ["Food Services-Jugo Juice CIF"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6207", "path": "/unit_6207", "attributes": {"unit_id": ["6207"], "full_name": ["Food Services-Jugo Juice SLC"], "short_name": ["Food Services-Jugo Juice SLC"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6226", "path": "/unit_6226", "attributes": {"unit_id": ["6226"], "full_name": ["Food Services-Liquid Assets"], "short_name": ["Food Services-Liquid Assets"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6215", "path": "/unit_6215", "attributes": {"unit_id": ["6215"], "full_name": ["Food Services-Marketing"], "short_name": ["Food Services-Marketing"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6218", "path": "/unit_6218", "attributes": {"unit_id": ["6218"], "full_name": ["Food Services-Operations"], "short_name": ["Food Services-Operations"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6247", "path": "/unit_6247", "attributes": {"unit_id": ["6247"], "full_name": ["Food Services-PAS Lounge"], "short_name": ["Food Services-PAS Lounge"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6213", "path": "/unit_6213", "attributes": {"unit_id": ["6213"], "full_name": ["Food Services-Pastry Plus Co-op"], "short_name": ["Food Services-Pastry Plus Co-op"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6224", "path": "/unit_6224", "attributes": {"unit_id": ["6224"], "full_name": ["Food Services-Pastry Plus NH"], "short_name": ["Food Services-Pastry Plus NH"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6560", "path": "/unit_6560", "attributes": {"unit_id": ["6560"], "full_name": ["Food Services-REV Conference Ctr"], "short_name": ["Food Services-REV Conference Ctr"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6242", "path": "/unit_6242", "attributes": {"unit_id": ["6242"], "full_name": ["Food Services-Ron <PERSON>t Village"], "short_name": ["Food Services-Ron <PERSON>t Village"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6235", "path": "/unit_6235", "attributes": {"unit_id": ["6235"], "full_name": ["Food Services-Starbucks AHS"], "short_name": ["Food Services-Starbucks AHS"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6225", "path": "/unit_6225", "attributes": {"unit_id": ["6225"], "full_name": ["Food Services-Starbucks STC"], "short_name": ["Food Services-Starbucks STC"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6230", "path": "/unit_6230", "attributes": {"unit_id": ["6230"], "full_name": ["Food Services-Tim Hortons DC"], "short_name": ["Food Services-Tim Hortons DC"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6231", "path": "/unit_6231", "attributes": {"unit_id": ["6231"], "full_name": ["Food Services-Tim <PERSON>s ML"], "short_name": ["Food Services-Tim <PERSON>s ML"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6229", "path": "/unit_6229", "attributes": {"unit_id": ["6229"], "full_name": ["Food Services-Tim Hortons SCH"], "short_name": ["Food Services-Tim Hortons SCH"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6232", "path": "/unit_6232", "attributes": {"unit_id": ["6232"], "full_name": ["Food Services-Tim Hortons SLC"], "short_name": ["Food Services-Tim Hortons SLC"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6234", "path": "/unit_6234", "attributes": {"unit_id": ["6234"], "full_name": ["Food Services-Tim <PERSON>s UWP"], "short_name": ["Food Services-Tim <PERSON>s UWP"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6237", "path": "/unit_6237", "attributes": {"unit_id": ["6237"], "full_name": ["Food Services-UWP Residence"], "short_name": ["Food Services-UWP Residence"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6246", "path": "/unit_6246", "attributes": {"unit_id": ["6246"], "full_name": ["Food Services-University Club"], "short_name": ["Food Services-University Club"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6236", "path": "/unit_6236", "attributes": {"unit_id": ["6236"], "full_name": ["Food Services-Vending"], "short_name": ["Food Services-Vending"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6239", "path": "/unit_6239", "attributes": {"unit_id": ["6239"], "full_name": ["Food Services-Village 1"], "short_name": ["Food Services-Village 1"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1675", "path": "/unit_1675", "attributes": {"unit_id": ["1675"], "full_name": ["French Studies"], "short_name": ["French Studies"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_7220", "path": "/unit_7220", "attributes": {"unit_id": ["7220"], "full_name": ["Friends of UW Foundation"], "short_name": ["Friends of UW Foundation"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2640", "path": "/unit_2640", "attributes": {"unit_id": ["2640"], "full_name": ["Future Cities Institute"], "short_name": ["Future Cities Institute"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5311", "path": "/unit_5311", "attributes": {"unit_id": ["5311"], "full_name": ["GSPA Marketing & Recruitment"], "short_name": ["GSPA Marketing & Recruitment"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5301", "path": "/unit_5301", "attributes": {"unit_id": ["5301"], "full_name": ["GSPA Office"], "short_name": ["GSPA Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5300", "path": "/unit_5300", "attributes": {"unit_id": ["5300"], "full_name": ["GSPA Scholarships & Bursaries"], "short_name": ["GSPA Scholarships & Bursaries"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5478", "path": "/unit_5478", "attributes": {"unit_id": ["5478"], "full_name": ["Games Institute"], "short_name": ["Games Institute"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2650", "path": "/unit_2650", "attributes": {"unit_id": ["2650"], "full_name": ["Geography & Environmental Mgmt"], "short_name": ["Geography & Environmental Mgmt"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1700", "path": "/unit_1700", "attributes": {"unit_id": ["1700"], "full_name": ["Germanic & Slavic Studies"], "short_name": ["Germanic & Slavic Studies"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5630", "path": "/unit_5630", "attributes": {"unit_id": ["5630"], "full_name": ["HR Special Projects"], "short_name": ["HR Special Projects"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5324", "path": "/unit_5324", "attributes": {"unit_id": ["5324"], "full_name": ["Health Promotion"], "short_name": ["Health Promotion"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1080", "path": "/unit_1080", "attributes": {"unit_id": ["1080"], "full_name": ["Health-Computing Office"], "short_name": ["Health-Computing Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1075", "path": "/unit_1075", "attributes": {"unit_id": ["1075"], "full_name": ["Health-Deans Office"], "short_name": ["Health-Deans Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1079", "path": "/unit_1079", "attributes": {"unit_id": ["1079"], "full_name": ["Health-Research Inst for Aging"], "short_name": ["Health-Research Inst for Aging"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1081", "path": "/unit_1081", "attributes": {"unit_id": ["1081"], "full_name": ["Health-Student & Information Serv"], "short_name": ["Health-Student & Information Serv"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1725", "path": "/unit_1725", "attributes": {"unit_id": ["1725"], "full_name": ["History"], "short_name": ["History"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5320", "path": "/unit_5320", "attributes": {"unit_id": ["5320"], "full_name": ["Hlth Serv-Student Medical Clinic"], "short_name": ["Hlth Serv-Student Medical Clinic"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_7200", "path": "/unit_7200", "attributes": {"unit_id": ["7200"], "full_name": ["Hong Kong Foundation"], "short_name": ["Hong Kong Foundation"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6475", "path": "/unit_6475", "attributes": {"unit_id": ["6475"], "full_name": ["Housing & Res Residence Facilities"], "short_name": ["Housing & Res Residence Facilities"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6600", "path": "/unit_6600", "attributes": {"unit_id": ["6600"], "full_name": ["Housing & Res Student Development"], "short_name": ["Housing & Res Student Development"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6565", "path": "/unit_6565", "attributes": {"unit_id": ["6565"], "full_name": ["Housing & Res-Administration"], "short_name": ["Housing & Res-Administration"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6453", "path": "/unit_6453", "attributes": {"unit_id": ["6453"], "full_name": ["Housing & Res-After Hours"], "short_name": ["Housing & Res-After Hours"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6480", "path": "/unit_6480", "attributes": {"unit_id": ["6480"], "full_name": ["Housing & Res-Cleaning Serv"], "short_name": ["Housing & Res-Cleaning Serv"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6457", "path": "/unit_6457", "attributes": {"unit_id": ["6457"], "full_name": ["Housing & Res-Desk Services"], "short_name": ["Housing & Res-Desk Services"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6452", "path": "/unit_6452", "attributes": {"unit_id": ["6452"], "full_name": ["Housing & Res-Enterprise Systems"], "short_name": ["Housing & Res-Enterprise Systems"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6454", "path": "/unit_6454", "attributes": {"unit_id": ["6454"], "full_name": ["Housing & Res-Facility Renewal"], "short_name": ["Housing & Res-Facility Renewal"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6455", "path": "/unit_6455", "attributes": {"unit_id": ["6455"], "full_name": ["Housing & Res-IST Services"], "short_name": ["Housing & Res-IST Services"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6456", "path": "/unit_6456", "attributes": {"unit_id": ["6456"], "full_name": ["Housing & Res-Living Learning"], "short_name": ["Housing & Res-Living Learning"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6470", "path": "/unit_6470", "attributes": {"unit_id": ["6470"], "full_name": ["Housing & Res-Maintenance"], "short_name": ["Housing & Res-Maintenance"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6465", "path": "/unit_6465", "attributes": {"unit_id": ["6465"], "full_name": ["Housing & Residence"], "short_name": ["Housing & Residence"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6450", "path": "/unit_6450", "attributes": {"unit_id": ["6450"], "full_name": ["Housing & Residence Occupancy"], "short_name": ["Housing & Residence Occupancy"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6451", "path": "/unit_6451", "attributes": {"unit_id": ["6451"], "full_name": ["Housing & Residence-Marketing"], "short_name": ["Housing & Residence-Marketing"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5330", "path": "/unit_5330", "attributes": {"unit_id": ["5330"], "full_name": ["Human Resources"], "short_name": ["Human Resources"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5471", "path": "/unit_5471", "attributes": {"unit_id": ["5471"], "full_name": ["IC3"], "short_name": ["IC3"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_8611", "path": "/unit_8611", "attributes": {"unit_id": ["8611"], "full_name": ["IQC-CFREF"], "short_name": ["IQC-CFREF"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5170", "path": "/unit_5170", "attributes": {"unit_id": ["5170"], "full_name": ["IST"], "short_name": ["IST"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5171", "path": "/unit_5171", "attributes": {"unit_id": ["5171"], "full_name": ["IST Campus Projects"], "short_name": ["IST Campus Projects"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5580", "path": "/unit_5580", "attributes": {"unit_id": ["5580"], "full_name": ["IST-Telephone Services"], "short_name": ["IST-Telephone Services"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5370", "path": "/unit_5370", "attributes": {"unit_id": ["5370"], "full_name": ["Indigenous Relations"], "short_name": ["Indigenous Relations"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_8610", "path": "/unit_8610", "attributes": {"unit_id": ["8610"], "full_name": ["Inst for Quantum Computing"], "short_name": ["Inst for Quantum Computing"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_8352", "path": "/unit_8352", "attributes": {"unit_id": ["8352"], "full_name": ["Institute for Polymer Research"], "short_name": ["Institute for Polymer Research"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5350", "path": "/unit_5350", "attributes": {"unit_id": ["5350"], "full_name": ["Institutional Analysis & Plan"], "short_name": ["Institutional Analysis & Plan"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5400", "path": "/unit_5400", "attributes": {"unit_id": ["5400"], "full_name": ["Integrated Planning & Budgeting Office"], "short_name": ["Integrated Planning & Budgeting Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1125", "path": "/unit_1125", "attributes": {"unit_id": ["1125"], "full_name": ["Kinesiology"], "short_name": ["Kinesiology"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1078", "path": "/unit_1078", "attributes": {"unit_id": ["1078"], "full_name": ["Kinesiology-CCCARE"], "short_name": ["Kinesiology-CCCARE"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2760", "path": "/unit_2760", "attributes": {"unit_id": ["2760"], "full_name": ["Knowledge Integration"], "short_name": ["Knowledge Integration"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5710", "path": "/unit_5710", "attributes": {"unit_id": ["5710"], "full_name": ["Legal & Immigration Services"], "short_name": ["Legal & Immigration Services"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5386", "path": "/unit_5386", "attributes": {"unit_id": ["5386"], "full_name": ["Library"], "short_name": ["Library"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5382", "path": "/unit_5382", "attributes": {"unit_id": ["5382"], "full_name": ["Market & Strat Comm-Creative Studio"], "short_name": ["Market & Strat Comm-Creative Studio"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5380", "path": "/unit_5380", "attributes": {"unit_id": ["5380"], "full_name": ["Market & Strategic Comm"], "short_name": ["Market & Strategic Comm"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3000", "path": "/unit_3000", "attributes": {"unit_id": ["3000"], "full_name": ["Math-Deans Office"], "short_name": ["Math-Deans Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3050", "path": "/unit_3050", "attributes": {"unit_id": ["3050"], "full_name": ["Math-Grad Office"], "short_name": ["Math-Grad Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3125", "path": "/unit_3125", "attributes": {"unit_id": ["3125"], "full_name": ["Math-MFCF"], "short_name": ["Math-MFCF"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3002", "path": "/unit_3002", "attributes": {"unit_id": ["3002"], "full_name": ["Math-Undergrad Adm & Outreach"], "short_name": ["Math-Undergrad Adm & Outreach"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3001", "path": "/unit_3001", "attributes": {"unit_id": ["3001"], "full_name": ["Math-Undergrad Office"], "short_name": ["Math-Undergrad Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3200", "path": "/unit_3200", "attributes": {"unit_id": ["3200"], "full_name": ["Mathematics Research Office"], "short_name": ["Mathematics Research Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2551", "path": "/unit_2551", "attributes": {"unit_id": ["2551"], "full_name": ["Mechatronics UG Engineering"], "short_name": ["Mechatronics UG Engineering"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2550", "path": "/unit_2550", "attributes": {"unit_id": ["2550"], "full_name": ["Nanotechnology Engineering"], "short_name": ["Nanotechnology Engineering"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3600", "path": "/unit_3600", "attributes": {"unit_id": ["3600"], "full_name": ["Optometry"], "short_name": ["Optometry"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3650", "path": "/unit_3650", "attributes": {"unit_id": ["3650"], "full_name": ["Optometry Clinic"], "short_name": ["Optometry Clinic"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6400", "path": "/unit_6400", "attributes": {"unit_id": ["6400"], "full_name": ["Parking"], "short_name": ["Parking"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3700", "path": "/unit_3700", "attributes": {"unit_id": ["3700"], "full_name": ["Pharmacy"], "short_name": ["Pharmacy"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1800", "path": "/unit_1800", "attributes": {"unit_id": ["1800"], "full_name": ["Philosophy"], "short_name": ["Philosophy"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3400", "path": "/unit_3400", "attributes": {"unit_id": ["3400"], "full_name": ["Physics & Astronomy"], "short_name": ["Physics & Astronomy"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2675", "path": "/unit_2675", "attributes": {"unit_id": ["2675"], "full_name": ["Planning"], "short_name": ["Planning"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5502", "path": "/unit_5502", "attributes": {"unit_id": ["5502"], "full_name": ["Plant Operations"], "short_name": ["Plant Operations"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5501", "path": "/unit_5501", "attributes": {"unit_id": ["5501"], "full_name": ["Plant Operations-Bldg Design"], "short_name": ["Plant Operations-Bldg Design"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5503", "path": "/unit_5503", "attributes": {"unit_id": ["5503"], "full_name": ["Plant Operations-Building"], "short_name": ["Plant Operations-Building"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5505", "path": "/unit_5505", "attributes": {"unit_id": ["5505"], "full_name": ["Plant Operations-Central Plant"], "short_name": ["Plant Operations-Central Plant"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5504", "path": "/unit_5504", "attributes": {"unit_id": ["5504"], "full_name": ["Plant Operations-Custodial"], "short_name": ["Plant Operations-Custodial"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5507", "path": "/unit_5507", "attributes": {"unit_id": ["5507"], "full_name": ["Plant Operations-Electrical"], "short_name": ["Plant Operations-Electrical"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5508", "path": "/unit_5508", "attributes": {"unit_id": ["5508"], "full_name": ["Plant Operations-Grounds"], "short_name": ["Plant Operations-Grounds"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5511", "path": "/unit_5511", "attributes": {"unit_id": ["5511"], "full_name": ["Plant Operations-Major Projects"], "short_name": ["Plant Operations-Major Projects"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5506", "path": "/unit_5506", "attributes": {"unit_id": ["5506"], "full_name": ["Plant Operations-Mechanical"], "short_name": ["Plant Operations-Mechanical"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5500", "path": "/unit_5500", "attributes": {"unit_id": ["5500"], "full_name": ["Plant Operations-Repairs & Alter"], "short_name": ["Plant Operations-Repairs & Alter"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5509", "path": "/unit_5509", "attributes": {"unit_id": ["5509"], "full_name": ["Plant Operations-Tool Crib"], "short_name": ["Plant Operations-Tool Crib"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5510", "path": "/unit_5510", "attributes": {"unit_id": ["5510"], "full_name": ["Plant Operations-Utilities"], "short_name": ["Plant Operations-Utilities"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1825", "path": "/unit_1825", "attributes": {"unit_id": ["1825"], "full_name": ["Political Science"], "short_name": ["Political Science"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5440", "path": "/unit_5440", "attributes": {"unit_id": ["5440"], "full_name": ["Presidents Office"], "short_name": ["Presidents Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6000", "path": "/unit_6000", "attributes": {"unit_id": ["6000"], "full_name": ["Print + Retail Solutions"], "short_name": ["Print + Retail Solutions"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6050", "path": "/unit_6050", "attributes": {"unit_id": ["6050"], "full_name": ["Print + Retail Solutions -Book Store"], "short_name": ["Print + Retail Solutions -Book Store"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6151", "path": "/unit_6151", "attributes": {"unit_id": ["6151"], "full_name": ["Print + Retail Solutions -Campus Tech"], "short_name": ["Print + Retail Solutions -Campus Tech"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6162", "path": "/unit_6162", "attributes": {"unit_id": ["6162"], "full_name": ["Print + Retail Solutions -Digital Prod"], "short_name": ["Print + Retail Solutions -Digital Prod"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6161", "path": "/unit_6161", "attributes": {"unit_id": ["6161"], "full_name": ["Print + Retail Solutions -Fleet Copiers"], "short_name": ["Print + Retail Solutions -Fleet Copiers"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6160", "path": "/unit_6160", "attributes": {"unit_id": ["6160"], "full_name": ["Print + Retail Solutions -Media Copy Centres"], "short_name": ["Print + Retail Solutions -Media Copy Centres"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6055", "path": "/unit_6055", "attributes": {"unit_id": ["6055"], "full_name": ["Print + Retail Solutions -Waterloo Store"], "short_name": ["Print + Retail Solutions -Waterloo Store"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6070", "path": "/unit_6070", "attributes": {"unit_id": ["6070"], "full_name": ["Print + Retail Solutions -Write Stuff"], "short_name": ["Print + Retail Solutions -Write Stuff"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6075", "path": "/unit_6075", "attributes": {"unit_id": ["6075"], "full_name": ["Print + Retail Solutions-W Store E-Commerce"], "short_name": ["Print + Retail Solutions-W Store E-Commerce"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1175", "path": "/unit_1175", "attributes": {"unit_id": ["1175"], "full_name": ["Propel"], "short_name": ["Propel"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1852", "path": "/unit_1852", "attributes": {"unit_id": ["1852"], "full_name": ["Psych-Early Childhood Educ"], "short_name": ["Psych-Early Childhood Educ"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1850", "path": "/unit_1850", "attributes": {"unit_id": ["1850"], "full_name": ["Psychology"], "short_name": ["Psychology"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1100", "path": "/unit_1100", "attributes": {"unit_id": ["1100"], "full_name": ["Public Health & Hlth Systems"], "short_name": ["Public Health & Hlth Systems"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1101", "path": "/unit_1101", "attributes": {"unit_id": ["1101"], "full_name": ["Public Hlth & Hlth Sys-Practice Ctr"], "short_name": ["Public Hlth & Hlth Sys-Practice Ctr"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3075", "path": "/unit_3075", "attributes": {"unit_id": ["3075"], "full_name": ["Pure Mathematics"], "short_name": ["Pure Mathematics"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1150", "path": "/unit_1150", "attributes": {"unit_id": ["1150"], "full_name": ["Recreation & Leisure Studies"], "short_name": ["Recreation & Leisure Studies"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5530", "path": "/unit_5530", "attributes": {"unit_id": ["5530"], "full_name": ["Registrars"], "short_name": ["Registrars"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5533", "path": "/unit_5533", "attributes": {"unit_id": ["5533"], "full_name": ["Registrars-Student Awards"], "short_name": ["Registrars-Student Awards"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5534", "path": "/unit_5534", "attributes": {"unit_id": ["5534"], "full_name": ["Registrars-Tuition Set Aside"], "short_name": ["Registrars-Tuition Set Aside"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5532", "path": "/unit_5532", "attributes": {"unit_id": ["5532"], "full_name": ["Registrars-Undergrad Recruit"], "short_name": ["Registrars-Undergrad Recruit"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1875", "path": "/unit_1875", "attributes": {"unit_id": ["1875"], "full_name": ["Religious Studies"], "short_name": ["Religious Studies"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1876", "path": "/unit_1876", "attributes": {"unit_id": ["1876"], "full_name": ["Religious Studies-5 Agency"], "short_name": ["Religious Studies-5 Agency"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_7300", "path": "/unit_7300", "attributes": {"unit_id": ["7300"], "full_name": ["Renison Univ College"], "short_name": ["Renison Univ College"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5470", "path": "/unit_5470", "attributes": {"unit_id": ["5470"], "full_name": ["Research Office"], "short_name": ["Research Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2750", "path": "/unit_2750", "attributes": {"unit_id": ["2750"], "full_name": ["SEED"], "short_name": ["SEED"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5431", "path": "/unit_5431", "attributes": {"unit_id": ["5431"], "full_name": ["SSO-AccessAbility Services"], "short_name": ["SSO-AccessAbility Services"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5650", "path": "/unit_5650", "attributes": {"unit_id": ["5650"], "full_name": ["SSO-Learning Services"], "short_name": ["SSO-Learning Services"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5680", "path": "/unit_5680", "attributes": {"unit_id": ["5680"], "full_name": ["SSO-Outreach & Engagement"], "short_name": ["SSO-Outreach & Engagement"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5433", "path": "/unit_5433", "attributes": {"unit_id": ["5433"], "full_name": ["SSO-Student Exper-First Year"], "short_name": ["SSO-Student Exper-First Year"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5481", "path": "/unit_5481", "attributes": {"unit_id": ["5481"], "full_name": ["SSO-Student Exper-International"], "short_name": ["SSO-Student Exper-International"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5540", "path": "/unit_5540", "attributes": {"unit_id": ["5540"], "full_name": ["Safety Office"], "short_name": ["Safety Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3354", "path": "/unit_3354", "attributes": {"unit_id": ["3354"], "full_name": ["Science & Business"], "short_name": ["Science & Business"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3425", "path": "/unit_3425", "attributes": {"unit_id": ["3425"], "full_name": ["Science Technical Services"], "short_name": ["Science Technical Services"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3360", "path": "/unit_3360", "attributes": {"unit_id": ["3360"], "full_name": ["Science WCMR"], "short_name": ["Science WCMR"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3351", "path": "/unit_3351", "attributes": {"unit_id": ["3351"], "full_name": ["Science-Computing Office"], "short_name": ["Science-Computing Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3350", "path": "/unit_3350", "attributes": {"unit_id": ["3350"], "full_name": ["Science-Deans Office"], "short_name": ["Science-Deans Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3352", "path": "/unit_3352", "attributes": {"unit_id": ["3352"], "full_name": ["Science-Facilities Office"], "short_name": ["Science-Facilities Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3356", "path": "/unit_3356", "attributes": {"unit_id": ["3356"], "full_name": ["Science-Medical Science"], "short_name": ["Science-Medical Science"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3355", "path": "/unit_3355", "attributes": {"unit_id": ["3355"], "full_name": ["Science-Outreach"], "short_name": ["Science-Outreach"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3353", "path": "/unit_3353", "attributes": {"unit_id": ["3353"], "full_name": ["Science-Undergrad Office"], "short_name": ["Science-Undergrad Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5550", "path": "/unit_5550", "attributes": {"unit_id": ["5550"], "full_name": ["Secretariat"], "short_name": ["Secretariat"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1900", "path": "/unit_1900", "attributes": {"unit_id": ["1900"], "full_name": ["Soc & Legal Studies"], "short_name": ["Soc & Legal Studies"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1901", "path": "/unit_1901", "attributes": {"unit_id": ["1901"], "full_name": ["South-Western Ont Rsch Data Ctr"], "short_name": ["South-Western Ont Rsch Data Ctr"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5401", "path": "/unit_5401", "attributes": {"unit_id": ["5401"], "full_name": ["Space Planning Office"], "short_name": ["Space Planning Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_1925", "path": "/unit_1925", "attributes": {"unit_id": ["1925"], "full_name": ["Spanish"], "short_name": ["Spanish"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5560", "path": "/unit_5560", "attributes": {"unit_id": ["5560"], "full_name": ["Special Constable Services"], "short_name": ["Special Constable Services"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_7800", "path": "/unit_7800", "attributes": {"unit_id": ["7800"], "full_name": ["St Jeromes University"], "short_name": ["St Jeromes University"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_3100", "path": "/unit_3100", "attributes": {"unit_id": ["3100"], "full_name": ["Statistics & Actuarial Science"], "short_name": ["Statistics & Actuarial Science"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5730", "path": "/unit_5730", "attributes": {"unit_id": ["5730"], "full_name": ["Student Service Centre"], "short_name": ["Student Service Centre"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5640", "path": "/unit_5640", "attributes": {"unit_id": ["5640"], "full_name": ["Student Success Office"], "short_name": ["Student Success Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5150", "path": "/unit_5150", "attributes": {"unit_id": ["5150"], "full_name": ["Sustainability Office"], "short_name": ["Sustainability Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5434", "path": "/unit_5434", "attributes": {"unit_id": ["5434"], "full_name": ["UWaterlooLife Communications"], "short_name": ["UWaterlooLife Communications"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_7325", "path": "/unit_7325", "attributes": {"unit_id": ["7325"], "full_name": ["United College"], "short_name": ["United College"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5450", "path": "/unit_5450", "attributes": {"unit_id": ["5450"], "full_name": ["VP Acad & Prov Office"], "short_name": ["VP Acad & Prov Office"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5451", "path": "/unit_5451", "attributes": {"unit_id": ["5451"], "full_name": ["VP Acad & Prov Office-Unalloc Budget"], "short_name": ["VP Acad & Prov Office-Unalloc Budget"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5420", "path": "/unit_5420", "attributes": {"unit_id": ["5420"], "full_name": ["VP Admin & Finance"], "short_name": ["VP Admin & Finance"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5426", "path": "/unit_5426", "attributes": {"unit_id": ["5426"], "full_name": ["VP Admin & Finance Project Mgmt & Change Mgmt"], "short_name": ["VP Admin & Finance Project Mgmt & Change Mgmt"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5425", "path": "/unit_5425", "attributes": {"unit_id": ["5425"], "full_name": ["VP Admin & Finance Records Management"], "short_name": ["VP Admin & Finance Records Management"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5424", "path": "/unit_5424", "attributes": {"unit_id": ["5424"], "full_name": ["VP Admin & Finance Risk Management"], "short_name": ["VP Admin & Finance Risk Management"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5422", "path": "/unit_5422", "attributes": {"unit_id": ["5422"], "full_name": ["VP Admin & Finance-R&T Park"], "short_name": ["VP Admin & Finance-R&T Park"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5423", "path": "/unit_5423", "attributes": {"unit_id": ["5423"], "full_name": ["VP Admin & Finance-Unalloc Budget"], "short_name": ["VP Admin & Finance-Unalloc Budget"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5460", "path": "/unit_5460", "attributes": {"unit_id": ["5460"], "full_name": ["VP Advancement"], "short_name": ["VP Advancement"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5252", "path": "/unit_5252", "attributes": {"unit_id": ["5252"], "full_name": ["VP Advancement Marketing and Communications"], "short_name": ["VP Advancement Marketing and Communications"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5476", "path": "/unit_5476", "attributes": {"unit_id": ["5476"], "full_name": ["VP Research-SOWC"], "short_name": ["VP Research-SOWC"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5474", "path": "/unit_5474", "attributes": {"unit_id": ["5474"], "full_name": ["VP Research-Water Institute"], "short_name": ["VP Research-Water Institute"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5691", "path": "/unit_5691", "attributes": {"unit_id": ["5691"], "full_name": ["VP <PERSON><PERSON>l-Community Rel & Events"], "short_name": ["VP <PERSON><PERSON>l-Community Rel & Events"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5690", "path": "/unit_5690", "attributes": {"unit_id": ["5690"], "full_name": ["VP Univ Relations"], "short_name": ["VP Univ Relations"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5693", "path": "/unit_5693", "attributes": {"unit_id": ["5693"], "full_name": ["VP Univ Relations-Comm"], "short_name": ["VP Univ Relations-Comm"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5692", "path": "/unit_5692", "attributes": {"unit_id": ["5692"], "full_name": ["VP Univ Relations-Govt Relations"], "short_name": ["VP Univ Relations-Govt Relations"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5477", "path": "/unit_5477", "attributes": {"unit_id": ["5477"], "full_name": ["VP University Research"], "short_name": ["VP University Research"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5475", "path": "/unit_5475", "attributes": {"unit_id": ["5475"], "full_name": ["VPR Core Research Facility"], "short_name": ["VPR Core Research Facility"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5670", "path": "/unit_5670", "attributes": {"unit_id": ["5670"], "full_name": ["Velocity"], "short_name": ["Velocity"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2475", "path": "/unit_2475", "attributes": {"unit_id": ["2475"], "full_name": ["WIN"], "short_name": ["WIN"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2480", "path": "/unit_2480", "attributes": {"unit_id": ["2480"], "full_name": ["WISE"], "short_name": ["WISE"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2485", "path": "/unit_2485", "attributes": {"unit_id": ["2485"], "full_name": ["WatCAR"], "short_name": ["WatCAR"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_8810", "path": "/unit_8810", "attributes": {"unit_id": ["8810"], "full_name": ["WatCo"], "short_name": ["WatCo"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5740", "path": "/unit_5740", "attributes": {"unit_id": ["5740"], "full_name": ["WatSPEED"], "short_name": ["WatSPEED"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_6710", "path": "/unit_6710", "attributes": {"unit_id": ["6710"], "full_name": ["Watcard"], "short_name": ["Watcard"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_2495", "path": "/unit_2495", "attributes": {"unit_id": ["2495"], "full_name": ["Waterloo AI Institute"], "short_name": ["Waterloo AI Institute"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5480", "path": "/unit_5480", "attributes": {"unit_id": ["5480"], "full_name": ["Waterloo International"], "short_name": ["Waterloo International"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_7210", "path": "/unit_7210", "attributes": {"unit_id": ["7210"], "full_name": ["Waterloo Jishu Ltd"], "short_name": ["Waterloo Jishu Ltd"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5570", "path": "/unit_5570", "attributes": {"unit_id": ["5570"], "full_name": ["Waterloo Ventures"], "short_name": ["Waterloo Ventures"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5610", "path": "/unit_5610", "attributes": {"unit_id": ["5610"], "full_name": ["Work-Learn Institute"], "short_name": ["Work-Learn Institute"], "abbreviation": [null], "level_number": ["None"]}}, {"name": "unit_5280", "path": "/unit_5280", "attributes": {"unit_id": ["5280"], "full_name": ["Writing & Communication Centre"], "short_name": ["Writing & Communication Centre"], "abbreviation": [null], "level_number": ["None"]}}], "users": [{"username": "m4alsaad", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "AL Saad", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1188"], "sso_id": ["M4ALSAAD"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "l2arora", "enabled": true, "email": "<EMAIL>", "firstName": "LAKSHAY", "lastName": "ARORA", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1204"], "sso_id": ["L2ARORA"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1178"], "sso_id": ["MAAGAARD"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "m2aajami", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1179"], "sso_id": ["M2AAJAMI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1180"], "sso_id": ["EIHAB"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "labella", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1181"], "sso_id": ["LABELLA"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "ha<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1182"], "sso_id": ["HABOUEEM"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "x<PERSON><PERSON>am", "enabled": true, "email": "<EMAIL>", "firstName": "Xavier", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1183"], "sso_id": ["XABRAHAM"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "ka<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1184"], "sso_id": ["KABURSHA"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "nmabukhd", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1185"], "sso_id": ["NMABUKHD"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "l2ah<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1186"], "sso_id": ["L2AHMADI"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1187"], "sso_id": ["AAIJAZI"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "r<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1189"], "sso_id": ["RALHAMMO"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Al-<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1190"], "sso_id": ["AALMAYAH"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "balgo<PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "Basheer", "lastName": "Algohi", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1191"], "sso_id": ["BALGOHI"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "a448ali", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1192"], "sso_id": ["A448ALI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "a23allen", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1193"], "sso_id": ["A23ALLEN"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "salum<PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1194"], "sso_id": ["SALUMURA"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "f3amiri", "enabled": true, "email": "<EMAIL>", "firstName": "Fariba", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1195"], "sso_id": ["F3AMIRI"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "m7andert", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Anderton", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1196"], "sso_id": ["M7ANDERT"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "reandrig", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1197"], "sso_id": ["REANDRIG"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1198"], "sso_id": ["PANGELO"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "wkannabl", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Anna<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1199"], "sso_id": ["WKANNABL"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "karagong", "enabled": true, "email": "<EMAIL>", "firstName": "Xochit<PERSON> Karin<PERSON>", "lastName": "Aragon Gaxiola", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1200"], "sso_id": ["KARAGONG"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "maraji", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1201"], "sso_id": ["MARAJI"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1202"], "sso_id": ["AARAMI"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "Maricor Jane", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1203"], "sso_id": ["MJARLOS"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "agatkins", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1205"], "sso_id": ["AGATKINS"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "a3atkins", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1206"], "sso_id": ["A3ATKINS"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "ma<PERSON>oin", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Aucoin", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1207"], "sso_id": ["MAUCOIN"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Aultman-<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1208"], "sso_id": ["LAULTMAN"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "Elhamsad<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1209"], "sso_id": ["EAZIMI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "h2aziz", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1210"], "sso_id": ["H2AZIZ"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "cazzi", "enabled": true, "email": "<EMAIL>", "firstName": "Charbel", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1211"], "sso_id": ["CAZZI"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1212"], "sso_id": ["HBAAJ"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "jbabe", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1213"], "sso_id": ["JBABE"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "c2bachma", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1214"], "sso_id": ["C2BACHMA"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "mbajcsy", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Bajcsy", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1215"], "sso_id": ["MBAJCSY"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "dban", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Ban", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1216"], "sso_id": ["DBAN"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "hsbansal", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Bansal", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1217"], "sso_id": ["HSBANSAL"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1218"], "sso_id": ["AMBARAKA"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "jabarby", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1219"], "sso_id": ["JABARBY"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "n<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Basir", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1220"], "sso_id": ["NBASIR"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "obasir", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Basir", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1221"], "sso_id": ["OBASIR"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "d3basu", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Basu", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1222"], "sso_id": ["D3BASU"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "n2basu", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Basu", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1223"], "sso_id": ["N2BASU"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "a2beaudo", "enabled": true, "email": "<EMAIL>", "firstName": "Amber", "lastName": "Beaudoin", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1224"], "sso_id": ["A2BEAUDO"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "sbedi", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1225"], "sso_id": ["SBEDI"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1226"], "sso_id": ["RBEKCI"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "j<PERSON>ning", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1227"], "sso_id": ["JBENNING"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "mjlben<PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1228"], "sso_id": ["MJLBENOI"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "kankar", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1229"], "sso_id": ["KANKAR"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "kbidwell", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1230"], "sso_id": ["KBIDWELL"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "sbirkett", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1231"], "sso_id": ["SBIRKETT"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "e<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Biro", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1232"], "sso_id": ["EJBIRO"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "wdbishop", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1233"], "sso_id": ["WDBISHOP"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "tmbisset", "enabled": true, "email": "<EMAIL>", "firstName": "Tara", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1234"], "sso_id": ["TMBISSET"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "a3blackw", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1235"], "sso_id": ["A3BLACKW"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "tboake", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Boake", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1236"], "sso_id": ["TBOAKE"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "j<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>ek<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1237"], "sso_id": ["JABOEKHO"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "<PERSON><PERSON>gh<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1238"], "sso_id": ["EBOGHAER"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "g<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1239"], "sso_id": ["GBOMMALI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "m<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Borland", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1240"], "sso_id": ["MJBORLAN"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "s<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1241"], "sso_id": ["SBOUMAIZ"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "a2boutro", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1242"], "sso_id": ["A2BOUTRO"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "Malgorzata", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1243"], "sso_id": ["MBRESTOV"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "cbrett", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1244"], "sso_id": ["CBRETT"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "a58brown", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1245"], "sso_id": ["A58BROWN"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "bmbarry", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1246"], "sso_id": ["BMBARRY"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "c37brown", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1247"], "sso_id": ["C37BROWN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "c62brown", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1248"], "sso_id": ["C62BROWN"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "<PERSON>bruck<PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "Orion", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1249"], "sso_id": ["OBRUCKMA"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "dj<PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Brush", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1250"], "sso_id": ["DJBRUSH"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "r6buchan", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1251"], "sso_id": ["R6BUCHAN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "h<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1252"], "sso_id": ["HBUDMAN"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "mbuettel", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1253"], "sso_id": ["MBUETTEL"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "c4burns", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1254"], "sso_id": ["C4BURNS"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "pageburt", "enabled": true, "email": "<EMAIL>", "firstName": "Page", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1255"], "sso_id": ["PAGEBURT"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "cbutcher", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1256"], "sso_id": ["CBUTCHER"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "k5campbe", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1257"], "sso_id": ["K5CAMPBE"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "sm2campb", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1258"], "sso_id": ["SM2CAMPB"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "<PERSON><PERSON>zar", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Can<PERSON>res", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1259"], "sso_id": ["CCANIZAR"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "s34cao", "enabled": true, "email": "<EMAIL>", "firstName": "Shi", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1260"], "sso_id": ["S34CAO"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "j2caron", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1261"], "sso_id": ["J2CARON"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "pdcarr", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1262"], "sso_id": ["PDCARR"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "m2chahil", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1263"], "sso_id": ["M2CHAHIL"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "Na<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1264"], "sso_id": ["NCHANDRA"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "whchang", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1265"], "sso_id": ["WHCHANG"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "<PERSON><PERSON>les", "enabled": true, "email": "<EMAIL>", "firstName": "Clarissa", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1266"], "sso_id": ["CCHARLES"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "cp<PERSON>u", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1267"], "sso_id": ["CPCHOU"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "a6christ", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1268"], "sso_id": ["A6CHRIST"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "d<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1269"], "sso_id": ["DCLAUSI"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "tcochran", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1270"], "sso_id": ["TCOCHRAN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "vbcoles", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1271"], "sso_id": ["VBCOLES"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "mcollins", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1272"], "sso_id": ["MCOLLINS"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "c4cooper", "enabled": true, "email": "<EMAIL>", "firstName": "Crystal", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1273"], "sso_id": ["C4COOPER"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "mstachow", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>ky", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1274"], "sso_id": ["MSTACHOW"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "dcorrea", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1275"], "sso_id": ["DCORREA"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "b2costa", "enabled": true, "email": "<EMAIL>", "firstName": "Brenna", "lastName": "Costa", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1276"], "sso_id": ["B2COSTA"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "r2costa", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Costa", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1277"], "sso_id": ["R2COSTA"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "jrcraig", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1278"], "sso_id": ["JRCRAIG"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "ecreager", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1279"], "sso_id": ["ECREAGER"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "kl3cress", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1280"], "sso_id": ["KL3CRESS"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "ecroiset", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1281"], "sso_id": ["ECROISET"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "ds<PERSON><PERSON>in", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1282"], "sso_id": ["DSCRONIN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "mcrowley", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1283"], "sso_id": ["MCROWLEY"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "vcui", "enabled": true, "email": "<EMAIL>", "firstName": "Victor", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1284"], "sso_id": ["VCUI"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "m<PERSON>l<PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Culham", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1285"], "sso_id": ["MCULHAM"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "ae2cunni", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1286"], "sso_id": ["AE2CUNNI"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "k2czarne", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1287"], "sso_id": ["K2CZARNE"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "c2dalcas", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1288"], "sso_id": ["C2DALCAS"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "mdamen", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1289"], "sso_id": ["MDAMEN"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "k<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1290"], "sso_id": ["KJDAUN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "kdautenh", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Dautenhahn", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1291"], "sso_id": ["KDAUTENH"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "tmda<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1292"], "sso_id": ["TMDAVIDS"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "ddavison", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1293"], "sso_id": ["DDAVISON"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "j<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1294"], "sso_id": ["JDETLOR"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1295"], "sso_id": ["DDEVARAJ"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "cdevaud", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1296"], "sso_id": ["CDEVAUD"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "n3dhaliw", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1297"], "sso_id": ["N3DHALIW"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "ldicecco", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>-<PERSON>", "lastName": "DiCecco", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1298"], "sso_id": ["LDICECCO"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "wdietl", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1299"], "sso_id": ["WDIETL"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "d<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1300"], "sso_id": ["DDIETZ"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1301"], "sso_id": ["SDIMITRO"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "Renate", "lastName": "Donnovan", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1302"], "sso_id": ["RDONNOVA"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "jdring", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1303"], "sso_id": ["JDRING"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "kldubois", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1304"], "sso_id": ["KLDUBOIS"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1305"], "sso_id": ["RDUIMERI"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "kdunne", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1306"], "sso_id": ["KDUNNE"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "mdu<PERSON>e", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1307"], "sso_id": ["MDUTHIE"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "aefstath", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1308"], "sso_id": ["AEFSTATH"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "r<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "El Shatshat", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1309"], "sso_id": ["RAELSHAT"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1310"], "sso_id": ["AHALHAJ"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Elbadrawy", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1311"], "sso_id": ["KMKHAIRY"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1312"], "sso_id": ["ELHEDHLI"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "m<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1313"], "sso_id": ["MBEMELKO"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "ferenay", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1314"], "sso_id": ["FERENAY"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "kaane", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Erkorkmaz", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1315"], "sso_id": ["KAANE"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "j3escoba", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Escobar", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1316"], "sso_id": ["J3ESCOBA"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "c<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>uler", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1317"], "sso_id": ["CEULER"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "rfallahf", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1318"], "sso_id": ["RFALLAHF"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "j2farnsw", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Farnsworth", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1319"], "sso_id": ["J2FARNSW"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "s<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1320"], "sso_id": ["SRFEENEY"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "xfeng", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1321"], "sso_id": ["XFENG"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "pferfolj", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Ferfolja", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1322"], "sso_id": ["PFERFOLJ"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "rferguso", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1323"], "sso_id": ["RFERGUSO"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "s26fergu", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1324"], "sso_id": ["S26FERGU"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "cj<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Fermin", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1325"], "sso_id": ["CJFERMIN"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "fidan", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1326"], "sso_id": ["FIDAN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "p<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1327"], "sso_id": ["PFIEGUTH"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "s4fische", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1328"], "sso_id": ["S4FISCHE"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "sfischme", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1329"], "sso_id": ["SFISCHME"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "m4fisher", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1330"], "sso_id": ["M4FISHER"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Fonseka", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1331"], "sso_id": ["JAFONSEK"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "rmforsyt", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>sy<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1332"], "sso_id": ["RMFORSYT"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "dtfortin", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Fortin", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1333"], "sso_id": ["DTFORTIN"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "mfowler", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1334"], "sso_id": ["MFOWLER"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "rafraser", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1335"], "sso_id": ["RAFRASER"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "lfu", "enabled": true, "email": "<EMAIL>", "firstName": "Liping", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1336"], "sso_id": ["LFU"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "mgalasze", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1337"], "sso_id": ["MGALASZE"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>ger", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1338"], "sso_id": ["HGAOUDA"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "vcgaudet", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1339"], "sso_id": ["VCGAUDET"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "tgawel", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1340"], "sso_id": ["TGAWEL"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1341"], "sso_id": ["CCGELLER"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "egeorgio", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1342"], "sso_id": ["EGEORGIO"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1343"], "sso_id": ["AGERLICH"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "s2ghadim", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1344"], "sso_id": ["S2GHADIM"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1345"], "sso_id": ["MGHAFURI"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "kghavam", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1346"], "sso_id": ["KGHAVAM"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "h2ghosh", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Ghosh", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1347"], "sso_id": ["H2GHOSH"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "aksequei", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1348"], "sso_id": ["AKSEQUEI"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "lgolab", "enabled": true, "email": "<EMAIL>", "firstName": "Lukasz", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1349"], "sso_id": ["LGOLAB"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "wgolab", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1350"], "sso_id": ["WGOLAB"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "ggomez", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1351"], "sso_id": ["GGOMEZ"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "z47gong", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>(<PERSON><PERSON><PERSON>)", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1352"], "sso_id": ["Z47GONG"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "ggong", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1353"], "sso_id": ["GGONG"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "mgorbet", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1354"], "sso_id": ["MGORBET"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "rkgordon", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1355"], "sso_id": ["RKGORDON"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "agorny", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Go<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1356"], "sso_id": ["AGORNY"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "jgostick", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Gostick", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1357"], "sso_id": ["JGOSTICK"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "ic<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1358"], "sso_id": ["ICGRAANS"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "jgraansm", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1359"], "sso_id": ["JGRAANSM"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "r<PERSON>cie", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1360"], "sso_id": ["RGRACIE"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "js<PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Green", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1361"], "sso_id": ["JSGREEN"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "lgreen", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Green", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1362"], "sso_id": ["LGREEN"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "ngriffet", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1363"], "sso_id": ["NGRIFFET"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "p3groh", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Gro<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1364"], "sso_id": ["P3GROH"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "jagrove", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Grove", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1365"], "sso_id": ["JAGROVE"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "agryguc", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "G<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1366"], "sso_id": ["AGRYGUC"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "v98gupta", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1367"], "sso_id": ["V98GUPTA"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "agurfink", "enabled": true, "email": "<EMAIL>", "firstName": "Arie", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1368"], "sso_id": ["AGURFINK"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Gutierrez", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1369"], "sso_id": ["LEGUTIER"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "fgzara", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Gzara", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1370"], "sso_id": ["FGZARA"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "chaas", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1371"], "sso_id": ["CHAAS"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "bhab<PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1372"], "sso_id": ["BHABICHE"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "Parsin", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1373"], "sso_id": ["PHAJIREZ"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "erhalden", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Haldenby", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1374"], "sso_id": ["ERHALDEN"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "m2hancoc", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1375"], "sso_id": ["M2HANCOC"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "dw<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Harder", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1376"], "sso_id": ["DWHARDER"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON>n", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1377"], "sso_id": ["AHASAN"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "bhauer", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1378"], "sso_id": ["BHAUER"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "q7he", "enabled": true, "email": "<EMAIL>", "firstName": "Qi<PERSON><PERSON>", "lastName": "He", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1379"], "sso_id": ["Q7HE"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "s283he", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "He", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1380"], "sso_id": ["S283HE"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "rhe<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1381"], "sso_id": ["RHECKTUS"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "bhelling", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Hellinga", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1382"], "sso_id": ["BHELLING"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1383"], "sso_id": ["LHENDEL"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1384"], "sso_id": ["DHERMAN"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "dherrima", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1385"], "sso_id": ["DHERRIMA"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "hhess", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1386"], "sso_id": ["HHESS"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "j6<PERSON>key", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Hickey", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1387"], "sso_id": ["J6HICKEY"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "k<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>rst", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1388"], "sso_id": ["KDHIRST"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "p4ho", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1389"], "sso_id": ["P4HO"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "c3holt", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1390"], "sso_id": ["C3HOLT"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "j2irwin", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1391"], "sso_id": ["J2IRWIN"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1392"], "sso_id": ["THRYNYK"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "j52hu", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Hu", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1393"], "sso_id": ["J52HU"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "t72hu", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Hu", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1394"], "sso_id": ["T72HU"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "y526hu", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Hu", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1395"], "sso_id": ["Y526HU"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "z399huan", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1396"], "sso_id": ["Z399HUAN"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "chulls", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Hulls", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1397"], "sso_id": ["CHULLS"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "m<PERSON>mel", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1398"], "sso_id": ["MHUMMEL"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "fhunsber", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1399"], "sso_id": ["FHUNSBER"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "r5hunter", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1400"], "sso_id": ["R5HUNTER"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "Ada", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1401"], "sso_id": ["ADAHURST"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1402"], "sso_id": ["MHURWITZ"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "n25ibrah", "enabled": true, "email": "<EMAIL>", "firstName": "Nadine", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1403"], "sso_id": ["N25IBRAH"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1404"], "sso_id": ["SIFEANYI"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "kinal", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Inal", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1405"], "sso_id": ["KINAL"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1406"], "sso_id": ["MIOANNID"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "a28javed", "enabled": true, "email": "<EMAIL>", "firstName": "ALI", "lastName": "JAVED", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1412"], "sso_id": ["A28JAVED"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "a5jaber", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Jaber", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1407"], "sso_id": ["A5JABER"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1408"], "sso_id": ["HJAHEDMO"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "heidi", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1409"], "sso_id": ["HEIDI"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "k2janzen", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1410"], "sso_id": ["K2JANZEN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Jardin", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1411"], "sso_id": ["RJARDIN"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "jaya<PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1413"], "sso_id": ["JAYARAM"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "soojeon", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Jeon", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1414"], "sso_id": ["SOOJEON"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1415"], "sso_id": ["DA3JOHNS"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "b<PERSON><PERSON><PERSON>", "enabled": true, "email": "b<PERSON><PERSON><EMAIL>", "firstName": "Bill", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1416"], "sso_id": ["BJOLLEY"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "m2kamkar", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1417"], "sso_id": ["M2KAMKAR"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "nachiket", "enabled": true, "email": "<EMAIL>", "firstName": "Nachiket", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1418"], "sso_id": ["NACHIKET"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "ckapsis", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1419"], "sso_id": ["CKAPSIS"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "r<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1420"], "sso_id": ["RKAPTEIN"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "k<PERSON>m", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1421"], "sso_id": ["KKARIM"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "a6karimi", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1422"], "sso_id": ["A6KARIMI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "mkazeran", "enabled": true, "email": "<EMAIL>", "firstName": "Mehr<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1423"], "sso_id": ["MKAZERAN"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "sm2kears", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1424"], "sso_id": ["SM2KEARS"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "kellend", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>don<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1425"], "sso_id": ["KELLEND"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "nkelly", "enabled": true, "email": "<EMAIL>", "firstName": "Nicola", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1426"], "sso_id": ["NKELLY"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "va2kerr", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1427"], "sso_id": ["VA2KERR"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1428"], "sso_id": ["AKHAJEPO"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Khamesee", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1429"], "sso_id": ["KHAMESEE"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "c<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1430"], "sso_id": ["CKHAMKEU"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "s9khan", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1431"], "sso_id": ["S9KHAN"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "khandani", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1432"], "sso_id": ["KHANDANI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "j658kim", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1433"], "sso_id": ["J658KIM"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "kekim", "enabled": true, "email": "<EMAIL>", "firstName": "Kun-<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1434"], "sso_id": ["KEKIM"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "ny3kim", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1435"], "sso_id": ["NY3KIM"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "r4klein", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1436"], "sso_id": ["R4KLEIN"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "maknight", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1437"], "sso_id": ["MAKNIGHT"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "sritzer", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1438"], "sso_id": ["SRITZER"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1439"], "sso_id": ["HKOLLER"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "bonkoo", "enabled": true, "email": "<EMAIL>", "firstName": "Bonwo<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1440"], "sso_id": ["BONKOO"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "b<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Koziarski", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1441"], "sso_id": ["BCLASSEN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "jl2mclea", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1442"], "sso_id": ["JL2MCLEA"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "ekubica", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Ku<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1443"], "sso_id": ["EKUBICA"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "gkurosad", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1444"], "sso_id": ["GKUROSAD"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "jkusins", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1445"], "sso_id": ["JKUSINS"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "c28kwan", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON> (Charles)", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1446"], "sso_id": ["C28KWAN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1447"], "sso_id": ["HJKWON"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "js2kwon", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1448"], "sso_id": ["JS2KWON"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1449"], "sso_id": ["DLACROIX"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1450"], "sso_id": ["EKLAENDE"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "p23lam", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Lam", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1451"], "sso_id": ["P23LAM"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "sheppler", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1452"], "sso_id": ["SHEPPLER"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "clashbro", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1453"], "sso_id": ["CLASHBRO"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "n<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Lashgarian Azad", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1454"], "sso_id": ["NLASHGAR"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "d24lau", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1455"], "sso_id": ["D24LAU"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "c28lee", "enabled": true, "email": "<EMAIL>", "firstName": "Czang-Ho", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1456"], "sso_id": ["C28LEE"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "pmlevine", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1457"], "sso_id": ["PMLEVINE"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "ekli", "enabled": true, "email": "<EMAIL>", "firstName": "Eugene", "lastName": "Li", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1458"], "sso_id": ["EKLI"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "a84li", "enabled": true, "email": "<EMAIL>", "firstName": "Tianyuan", "lastName": "Li", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1460"], "sso_id": ["A84LI"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "x6li", "enabled": true, "email": "<EMAIL>", "firstName": "Xianguo", "lastName": "Li", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1461"], "sso_id": ["X6LI"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "y382li", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Li", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1462"], "sso_id": ["Y382LI"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "fs<PERSON>n", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1463"], "sso_id": ["FSLIEN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "flimtung", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1464"], "sso_id": ["FLIMTUNG"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "y2427liu", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1465"], "sso_id": ["Y2427LIU"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "y294liu", "enabled": true, "email": "<EMAIL>", "firstName": "Yilan", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1466"], "sso_id": ["Y294LIU"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "srl<PERSON>a", "enabled": true, "email": "<EMAIL>", "firstName": "Sergio", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1467"], "sso_id": ["SRLIVERA"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "jrlong", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1468"], "sso_id": ["JRLONG"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "z272lu", "enabled": true, "email": "<EMAIL>", "firstName": "Zhongming", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1469"], "sso_id": ["Z272LU"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "alund", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Lund", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1470"], "sso_id": ["ALUND"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "j43ma", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Ma", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1471"], "sso_id": ["J43MA"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "m27ma", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Ma", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1472"], "sso_id": ["M27MA"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "e26macdo", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1473"], "sso_id": ["E26MACDO"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "cgmacgre", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1475"], "sso_id": ["CGMACGRE"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "b<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "MacVicar", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1476"], "sso_id": ["BMACVICA"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "mpmacdon", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1474"], "sso_id": ["MPMACDON"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "nmaftoon", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Maftoon", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1477"], "sso_id": ["NMAFTOON"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "v2magdan", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Magdanz", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1478"], "sso_id": ["V2MAGDAN"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "j2maglia", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Magliaro", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1479"], "sso_id": ["J2MAGLIA"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "j2hu<PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1480"], "sso_id": ["J2HUTTON"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1481"], "sso_id": ["PMAHMOUD"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "h6mahmou", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1482"], "sso_id": ["H6MAHMOU"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1483"], "sso_id": ["AHMAJEDI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "ssmalhot", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1484"], "sso_id": ["SSMALHOT"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1485"], "sso_id": ["TMALHOTR"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "a3malloy", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1486"], "sso_id": ["A3MALLOY"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "sksmanap", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Manapragada", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1487"], "sso_id": ["SKSMANAP"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "c<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1488"], "sso_id": ["CGMANNES"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "rrmansou", "enabled": true, "email": "<EMAIL>", "firstName": "Ra<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1489"], "sso_id": ["RRMANSOU"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "h2marsha", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1490"], "sso_id": ["H2MARSHA"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "amartine", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1491"], "sso_id": ["AMARTINE"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "dwmather", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1492"], "sso_id": ["DWMATHER"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON>er", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1493"], "sso_id": ["MMAYER"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "lmmccart", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1494"], "sso_id": ["LMMCCART"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "cpmcclel", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1495"], "sso_id": ["CPMCCLEL"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "cmcdouga", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1496"], "sso_id": ["CMCDOUGA"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "m7mcgreg", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1497"], "sso_id": ["M7MCGREG"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1498"], "sso_id": ["KMCKAY"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "s2mclach", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1499"], "sso_id": ["S2MCLACH"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "jcmcminn", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1500"], "sso_id": ["JCMCMINN"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "mcphee", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1501"], "sso_id": ["MCPHEE"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "bmc<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1502"], "sso_id": ["BMCQUARR"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "cmcwebb", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1738"], "sso_id": ["cmcwebb"], "primary_unit_id": ["1975"], "unit_name": ["Stratford School of Interaction Design and Business"], "unit_abbreviation": ["STRATFORD"]}, "groups": ["/unit_1975"]}, {"username": "tmekonne", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>nen", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1503"], "sso_id": ["TMEKONNE"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "w<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1504"], "sso_id": ["WMELEK"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "mmer<PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1505"], "sso_id": ["MMERLAU"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "s<PERSON>cer", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1506"], "sso_id": ["SMMERCER"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "g2miao", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1507"], "sso_id": ["G2MIAO"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "vj<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1508"], "sso_id": ["VJMICHAE"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "olegm", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1509"], "sso_id": ["OLEGM"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "je2mille", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1510"], "sso_id": ["JE2MILLE"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "ajbmilne", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1511"], "sso_id": ["AJBMILNE"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "pmitran", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1512"], "sso_id": ["PMITRAN"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "jkmoll", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1513"], "sso_id": ["JKMOLL"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "kmombaur", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Mom<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1514"], "sso_id": ["KMOMBAUR"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "gmontesa", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON> (John)", "lastName": "Montesano", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1515"], "sso_id": ["GMONTESA"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "martha", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1516"], "sso_id": ["MARTHA"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "cmoresol", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1517"], "sso_id": ["CMORESOL"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "hmm<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1518"], "sso_id": ["HMMORGAN"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "r<PERSON>riso", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1519"], "sso_id": ["RMORRISO"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "a<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1520"], "sso_id": ["ARRMORTO"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1521"], "sso_id": ["DMUELLER"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "c2muntea", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1522"], "sso_id": ["C2MUNTEA"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "c2murad", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1523"], "sso_id": ["C2MURAD"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "kmusselm", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1524"], "sso_id": ["KMUSSELM"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1525"], "sso_id": ["HNAFISSI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "snaik", "enabled": true, "email": "<EMAIL>", "firstName": "Kshirasagar", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1526"], "sso_id": ["SNAIK"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "nairn", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1527"], "sso_id": ["NAIRN"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "c<PERSON><PERSON>v", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Nehaniv", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1528"], "sso_id": ["CNEHANIV"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "c2pearce", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1529"], "sso_id": ["C2PEARCE"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "e5ngo", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1530"], "sso_id": ["E5NGO"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "s7ngo", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1531"], "sso_id": ["S7NGO"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1532"], "sso_id": ["CNIELSEN"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "pnieva", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1533"], "sso_id": ["PNIEVA"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "r2nishid", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1534"], "sso_id": ["R2NISHID"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "kbfugard", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1535"], "sso_id": ["KBFUGARD"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "snosewor", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Noseworthy", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1536"], "sso_id": ["SNOSEWOR"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "gnot<PERSON>s", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Notomista", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1537"], "sso_id": ["GNOTOMIS"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "jnjolajo", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1538"], "sso_id": ["JNJOLAJO"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "sonay", "enabled": true, "email": "<EMAIL>", "firstName": "Se<PERSON><PERSON>k", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1539"], "sso_id": ["SONAY"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "bowen", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1540"], "sso_id": ["BOWEN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "dpacey", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1541"], "sso_id": ["DPACEY"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "rpal", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Pal", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1542"], "sso_id": ["RPAL"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "z79pan", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Pan", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1543"], "sso_id": ["Z79PAN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "mdpandey", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1544"], "sso_id": ["MDPANDEY"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "yv2pant", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1545"], "sso_id": ["YV2PANT"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "wj<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1546"], "sso_id": ["WJPARKER"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "apastrik", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1547"], "sso_id": ["APASTRIK"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "hdpatel", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1548"], "sso_id": ["HDPATEL"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "a2paxton", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1549"], "sso_id": ["A2PAXTON"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "rpellizz", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1550"], "sso_id": ["RPELLIZZ"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "p5peng", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1551"], "sso_id": ["P5PENG"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "j56peng", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1552"], "sso_id": ["J56PENG"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1553"], "sso_id": ["PENLIDIS"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "wpenney", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1554"], "sso_id": ["WPENNEY"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "g<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1555"], "sso_id": ["GPERALTA"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "peterson", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1556"], "sso_id": ["PETERSON"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "a34pham", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1557"], "sso_id": ["A34PHAM"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "aep<PERSON>s", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Pinos", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1558"], "sso_id": ["AEPINOS"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "mpirnia", "enabled": true, "email": "<EMAIL>", "firstName": "Mehr<PERSON>", "lastName": "Pirnia", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1559"], "sso_id": ["MPIRNIA"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "s3azad", "enabled": true, "email": "<EMAIL>", "firstName": "Sahar", "lastName": "Pirooz Azad", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1560"], "sso_id": ["S3AZAD"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "polak", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1561"], "sso_id": ["POLAK"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "ipolowa", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Polowa", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1562"], "sso_id": ["IPOLOWA"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "c<PERSON><PERSON>oy", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Pomeroy", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1563"], "sso_id": ["CPOMEROY"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "c3pope", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1564"], "sso_id": ["C3POPE"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "m2pope", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1565"], "sso_id": ["M2POPE"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "da2porte", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1566"], "sso_id": ["DA2PORTE"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1567"], "sso_id": ["SPOTAPEN"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "m2poudin", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1568"], "sso_id": ["M2POUDIN"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "prae<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1569"], "sso_id": ["PRAETZEL"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "eprince", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Prince", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1570"], "sso_id": ["EPRINCE"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "mmp<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "Maya", "lastName": "Przybylski", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1571"], "sso_id": ["MMPRZYBY"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "jpulsiph", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1572"], "sso_id": ["JPULSIPH"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "lquast", "enabled": true, "email": "<EMAIL>", "firstName": "Lorraine", "lastName": "Quast", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1573"], "sso_id": ["LQUAST"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "j<PERSON>lty", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Quilty", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1574"], "sso_id": ["JQUILTY"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "mm2qures", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1575"], "sso_id": ["MM2QURES"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1576"], "sso_id": ["ORAMAHI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "s<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1577"], "sso_id": ["SRAMBHAT"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "krampers", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Ram<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1578"], "sso_id": ["KRAMPERS"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "yrangom", "enabled": true, "email": "<EMAIL>", "firstName": "Yverick", "lastName": "Rangom", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1579"], "sso_id": ["YRANGOM"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "j2rank", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Rank", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1580"], "sso_id": ["J2RANK"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "j<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1581"], "sso_id": ["JRATTHAP"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "drayside", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1582"], "sso_id": ["DRAYSIDE"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "r4reddy", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1583"], "sso_id": ["R4REDDY"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1584"], "sso_id": ["MREIMER"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "c3ren", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Ren", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1585"], "sso_id": ["C3REN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "cren<PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1586"], "sso_id": ["CRENNICK"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "treza", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1587"], "sso_id": ["TREZA"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "laricard", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1588"], "sso_id": ["LARICARD"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1589"], "sso_id": ["NMRIPLEY"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "j2rivas", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Rivas", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1590"], "sso_id": ["J2RIVAS"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "ma2robin", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1591"], "sso_id": ["MA2ROBIN"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "s3roman", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Roman", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1592"], "sso_id": ["S3ROMAN"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "d9rose", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1593"], "sso_id": ["D9ROSE"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "cath", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1594"], "sso_id": ["CATH"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "j2<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1595"], "sso_id": ["J2ROSSI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "r<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1596"], "sso_id": ["RROUFAIL"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "vrynnime", "enabled": true, "email": "<EMAIL>", "firstName": "Valerio", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1597"], "sso_id": ["VRYNNIME"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "jf<PERSON>s", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1598"], "sso_id": ["JFHANNAS"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "rsaari", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Saari", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1599"], "sso_id": ["RSAARI"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "msachdev", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Sachdev", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1600"], "sso_id": ["MSACHDEV"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "b2sadegh", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1601"], "sso_id": ["B2SADEGH"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "fsafayen", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1602"], "sso_id": ["FSAFAYEN"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "sssaini", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1603"], "sso_id": ["SSSAINI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "m<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Salama", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1604"], "sso_id": ["MSALAMA"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "salehian", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Salehian", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1605"], "sso_id": ["SALEHIAN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "fsalim", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1606"], "sso_id": ["FSALIM"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "a2salsal", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Sal<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1607"], "sso_id": ["A2SALSAL"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "s5<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1608"], "sso_id": ["S5SAMUEL"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "mahindra", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1609"], "sso_id": ["MAHINDRA"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "asarfare", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1610"], "sso_id": ["ASARFARE"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "m<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1611"], "sso_id": ["MSCHERER"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "n3schmid", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1612"], "sso_id": ["N3SCHMID"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "gerrys", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1613"], "sso_id": ["GERRYS"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "schneide", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1614"], "sso_id": ["SCHNEIDE"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "o2schnei", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1615"], "sso_id": ["O2SCHNEI"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "kschoole", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1616"], "sso_id": ["KSCHOOLE"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "s4schulz", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1617"], "sso_id": ["S4SCHULZ"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "ka3scott", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1618"], "sso_id": ["KA3SCOTT"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "s6sengup", "enabled": true, "email": null, "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1619"], "sso_id": ["S6SENGUP"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "ksevo", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Sevo", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1620"], "sso_id": ["KSEVO"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "manisha", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1621"], "sso_id": ["MANISHA"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "mushah", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1622"], "sso_id": ["MUSHAH"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1623"], "sso_id": ["HSHAHSAV"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "wshang", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1624"], "sso_id": ["WSHANG"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1625"], "sso_id": ["HSHAVAND"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "c<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1626"], "sso_id": ["CSHELLEY"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "h22shen", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1627"], "sso_id": ["H22SHEN"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "sshen", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1628"], "sso_id": ["SSHEN"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "Lola", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1629"], "sso_id": ["LSHEPPAR"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "b2shilli", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>llingford", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1630"], "sso_id": ["B2SHILLI"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1631"], "sso_id": ["DSIMAKOV"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "l<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "Leonardo", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1632"], "sso_id": ["LSIMON"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "r5singh", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1633"], "sso_id": ["R5SINGH"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "sivothth", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1634"], "sso_id": ["SIVOTHTH"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "eskibick", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1635"], "sso_id": ["ESKIBICK"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "j4<PERSON>ne", "enabled": true, "email": "<EMAIL>", "firstName": "Jen", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1636"], "sso_id": ["J4SKINNE"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "sl2smith", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1637"], "sso_id": ["SL2SMITH"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "trsmoute", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1638"], "sso_id": ["TRSMOUTE"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "msmucker", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>cker", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1639"], "sso_id": ["MSMUCKER"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "msobon", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Sobon", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1640"], "sso_id": ["MSOBON"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "disparke", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Sparkes", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1641"], "sso_id": ["DISPARKE"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "shirley", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1642"], "sso_id": ["SHIRLEY"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "h<PERSON>ler", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Staller", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1643"], "sso_id": ["HSTALLER"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "jfstraub", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1644"], "sso_id": ["JFSTRAUB"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "s3su", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Su", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1645"], "sso_id": ["S3SU"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "t33su", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Su", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1646"], "sso_id": ["T33SU"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "n<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1647"], "sso_id": ["NSUMI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "m<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1648"], "sso_id": ["MSYMONS"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "mcsyms", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Syms", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1649"], "sso_id": ["MCSYMS"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "lszepani", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Szepaniak", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1650"], "sso_id": ["LSZEPANI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "ltahvild", "enabled": true, "email": "<EMAIL>", "firstName": "Ladan", "lastName": "Tahvildari", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1651"], "sso_id": ["LTAHVILD"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "mkctam", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Tam", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1652"], "sso_id": ["MKCTAM"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "b37tan", "enabled": true, "email": "<EMAIL>", "firstName": "Bin", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1653"], "sso_id": ["B37TAN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1654"], "sso_id": ["PTAVASSO"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "pmteerts", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1655"], "sso_id": ["PMTEERTS"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "s3tesfam", "enabled": true, "email": "<EMAIL>", "firstName": "Solomon", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1656"], "sso_id": ["S3TESFAM"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "q3thai", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>uan", "lastName": "Thai", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1657"], "sso_id": ["Q3THAI"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1658"], "sso_id": ["ATHAKKAR"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "jthistle", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Thistle", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1659"], "sso_id": ["JTHISTLE"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "pnrthomp", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1660"], "sso_id": ["PNRTHOMP"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "b<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1661"], "sso_id": ["BTOLSON"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "etoyserk", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Toyserkani", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1662"], "sso_id": ["ETOYSERK"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "b<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1663"], "sso_id": ["BPTRIPP"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "tripunit", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Tripunitara", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1664"], "sso_id": ["TRIPUNIT"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "tttsui", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>g", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1665"], "sso_id": ["TTTSUI"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "j6tung", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1666"], "sso_id": ["J6TUNG"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "ctzogan", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1667"], "sso_id": ["CTZOGAN"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "sutter", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1668"], "sso_id": ["SUTTER"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "m<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1670"], "sso_id": ["MVANDYKE"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "r<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1671"], "sso_id": ["RJVANPEL"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "m35vande", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1669"], "sso_id": ["M35VANDE"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "srvandek", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>ck<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1672"], "sso_id": ["SRVANDEK"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "kvaniea", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1673"], "sso_id": ["KVANIEA"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "ovechtom", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1674"], "sso_id": ["OVECHTOM"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "s<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Vin<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1675"], "sso_id": ["SVINTAN"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "mj<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1676"], "sso_id": ["MJVITELL"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "mlvlasea", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Vlasea", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1677"], "sso_id": ["MLVLASEA"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Vossen", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1678"], "sso_id": ["SVOSSEN"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "s<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Walbridge", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1679"], "sso_id": ["SWALBRID"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "j<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1680"], "sso_id": ["JWALGATE"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Walton", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1681"], "sso_id": ["MWALTON"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "z70wang", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1682"], "sso_id": ["Z70WANG"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "pasward", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Ward", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1683"], "sso_id": ["PASWARD"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "vward", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Ward", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1684"], "sso_id": ["VWARD"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1685"], "sso_id": ["ZWASILEW"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "m6weber", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1686"], "sso_id": ["M6WEBER"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "l28wei", "enabled": true, "email": "<EMAIL>", "firstName": "Lan", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1687"], "sso_id": ["L28WEI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "s23wei", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1688"], "sso_id": ["S23WEI"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "mawells", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1689"], "sso_id": ["MAWELLS"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "j<PERSON>wen", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1690"], "sso_id": ["JZWEN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "swi<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Widdifield", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1691"], "sso_id": ["SWIDDIFI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "twilkins", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1692"], "sso_id": ["TWILKINS"], "primary_unit_id": ["4000"], "unit_name": ["Conrad School of Entrepreneurship and Business"], "unit_abbreviation": ["CONRAD"]}, "groups": ["/unit_4000"]}, {"username": "twillett", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1693"], "sso_id": ["TWILLETT"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1694"], "sso_id": ["MWILLSON"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "cc4wilso", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1695"], "sso_id": ["CC4WILSO"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "te<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1696"], "sso_id": ["TEWINTON"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "a28wong", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1697"], "sso_id": ["A28WONG"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "wswong", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1698"], "sso_id": ["WSWONG"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "dw2wrigh", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1699"], "sso_id": ["DW2WRIGH"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "x369wu", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1700"], "sso_id": ["X369WU"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "y785wu", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1701"], "sso_id": ["Y785WU"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "h32xiao", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1702"], "sso_id": ["H32XIAO"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "llxie", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1703"], "sso_id": ["LLXIE"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "xie", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1704"], "sso_id": ["XIE"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "lxu", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1705"], "sso_id": ["LXU"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "y75yan", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Yan", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1706"], "sso_id": ["Y75YAN"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "ehyang", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1707"], "sso_id": ["EHYANG"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "h466yang", "enabled": true, "email": "<EMAIL>", "firstName": "Hongxia", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1708"], "sso_id": ["H466YANG"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "j634yang", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1709"], "sso_id": ["J634YANG"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "syarus", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1710"], "sso_id": ["SYARUS"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "myavuz", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Yavuz", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1711"], "sso_id": ["MYAVUZ"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "jyeow", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Yeow", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1712"], "sso_id": ["JYEOW"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "cmyeum", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1713"], "sso_id": ["CMYEUM"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "b7yi", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1714"], "sso_id": ["B7YI"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1715"], "sso_id": ["EYIM"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "s2yin", "enabled": true, "email": "<EMAIL>", "firstName": "Shunde", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1716"], "sso_id": ["S2YIN"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "ykyoon", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>on", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1717"], "sso_id": ["YKYOON"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "tyoworsk", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1718"], "sso_id": ["TYOWORSK"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1719"], "sso_id": ["AIPINGYU"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "a<PERSON>u", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1720"], "sso_id": ["ACHYU"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "smza<PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1721"], "sso_id": ["SMZAHEDI"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1722"], "sso_id": ["JZARNETT"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Zelek", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1723"], "sso_id": ["JZELEK"], "primary_unit_id": ["2450"], "unit_name": ["Department of Systems Design Engineering"], "unit_abbreviation": ["SDE"]}, "groups": ["/unit_2450"]}, {"username": "j2624zha", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1724"], "sso_id": ["J2624ZHA"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "l98zhang", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1725"], "sso_id": ["L98ZHANG"], "primary_unit_id": ["2600"], "unit_name": ["School of Architecture"], "unit_abbreviation": ["ARCH"]}, "groups": ["/unit_2600"]}, {"username": "m78zhang", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON> (John)", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1726"], "sso_id": ["M78ZHANG"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "<PERSON><PERSON><PERSON>", "enabled": true, "email": "<EMAIL>", "firstName": "Boxin", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1727"], "sso_id": ["ZHAOB"], "primary_unit_id": ["2100"], "unit_name": ["Department of Chemical Engineering"], "unit_abbreviation": ["CHE"]}, "groups": ["/unit_2100"]}, {"username": "nzhou", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1728"], "sso_id": ["NZHOU"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "f29zhu", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1729"], "sso_id": ["F29ZHU"], "primary_unit_id": ["2175"], "unit_name": ["Dean's Office, Faculty of Engineering"], "unit_abbreviation": ["DOE"]}, "groups": ["/unit_2175"]}, {"username": "k34zhu", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1730"], "sso_id": ["K34ZHU"], "primary_unit_id": ["2400"], "unit_name": ["Department of Management Science and Engineering"], "unit_abbreviation": ["MSE"]}, "groups": ["/unit_2400"]}, {"username": "wzhuang", "enabled": true, "email": "<EMAIL>", "firstName": "Weihua", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1731"], "sso_id": ["WZHUANG"], "primary_unit_id": ["2200"], "unit_name": ["Department of Electrical and Computer Engineering"], "unit_abbreviation": ["ECE"]}, "groups": ["/unit_2200"]}, {"username": "jzou", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1732"], "sso_id": ["JZOU"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}, {"username": "c<PERSON>ll", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1733"], "sso_id": ["CZURELL"], "primary_unit_id": ["2125"], "unit_name": ["Department of Civil and Environmental Engineering"], "unit_abbreviation": ["CEE"]}, "groups": ["/unit_2125"]}, {"username": "y364li", "enabled": true, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "li", "credentials": [{"type": "password", "value": "TempPassword123!", "temporary": true}], "attributes": {"faculty_id": ["1459"], "sso_id": ["Y364LI"], "primary_unit_id": ["2425"], "unit_name": ["Department of Mechanical and Mechatronics Engineering"], "unit_abbreviation": ["MME"]}, "groups": ["/unit_2425"]}]}