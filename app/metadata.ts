import type { Metadata } from 'next';

export const metadata: Metadata = {
  metadataBase: new URL('http://localhost:3000'),
  title: {
    template: '%s | <PERSON>',
    default: 'Amelia - Faculty Information System',
  },
  description: 'A comprehensive faculty information system for managing faculty data, activities, and merit reviews.',
  icons: {
    icon: [
      {
        url: '/favicon.ico',
        sizes: '32x32',
        type: 'image/x-icon',
      },
      {
        url: '/favicon-16x16.png',
        sizes: '16x16',
        type: 'image/png',
      },
      {
        url: '/favicon-32x32.png',
        sizes: '32x32',
        type: 'image/png',
      },
    ],
    apple: {
      url: '/apple-touch-icon.png',
      sizes: '180x180',
      type: 'image/png',
    },
  },
  manifest: '/site.webmanifest',
  applicationName: 'Amelia',
  appleWebApp: {
    capable: true,
    title: '<PERSON>',
    statusBarStyle: 'default',
  },
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    type: 'website',
    siteName: '<PERSON>',
    title: 'Amelia - Faculty Information System',
    description: 'A comprehensive faculty information system for managing faculty data, activities, and merit reviews.',
    images: [
      {
        url: '/opengraph-image.png',
        width: 1200,
        height: 630,
        alt: '<PERSON>',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Amelia - Faculty Information System',
    description: 'A comprehensive faculty information system for managing faculty data, activities, and merit reviews.',
    images: ['/opengraph-image.png'],
  },
};
