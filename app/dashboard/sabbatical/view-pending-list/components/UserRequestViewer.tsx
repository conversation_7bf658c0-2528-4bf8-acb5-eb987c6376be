'use client';

import { useState, useEffect } from 'react';
import DOMPurify from 'dompurify';
import { PlusIcon } from '@heroicons/react/24/outline';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Simple X icon component
const XIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <line x1="18" y1="6" x2="6" y2="18"></line>
    <line x1="6" y1="6" x2="18" y2="18"></line>
  </svg>
);

interface LeaveType {
  type: string;
  from: string;
  to: string;
  salaryArrangement: string;
}

interface SupervisorEntry{
  student_name: string;
  supervisor: string;
};

interface PersonnelSchema{
  name_position: string;
  rate_per_annum: string;
  hours_per_week: string;
  calc_rate: string;
  fringe_benefit: string;
  est_expenses: string;
  cost: string;
};

interface TravelSchema{
  location: string;
  duration: string;
  travel_mode: string;
  related_cost: string;
  cost: string;
};

interface OtherExpensesSchema{
  quantity: string;
  description: string;
  unit_cost: string;
  cost: string;
};

interface SabbRequest {
  appl_id:string;
  name: string;
  department: string;
  rank : string;
  tenure: string;
  first_rank: string;
  first_date: string;
  leave_outline: string;
  current_request_time_date: string;
  service_credit_remaining: string;
  grt_lieu_salary: string;
  list_of_publication: string;
  subbatical_travel_purpose: string;
  research_project: string;
  involved_in_research: string;
  sabbatical_contribution: string;
  past_leaves?: LeaveType[];
  current_leaves?: LeaveType[];
  non_teach_term?: LeaveType[];

  stp_2_leave_values: string;
  stp_2_hold_funding: string;
  stp_2_era_funding: string;
  stp_2_era_supervision: string;
  stp_2_out_rsrch_funding: string;
  stp_2_decl_out_emply: string;
  stp_2_pl_report: string;

  dept_name: string;
  applicant_name: string;
  date_from: string;
  date_to: string;
  delegated_supervisor: SupervisorEntry[];
  no_grad_student: string;

  lab_room_number: string;
  abscensce_date: string;
  delg_supervisor: string;
  dept_head: string;
  lab_responsibility: string;
  emergency_name: string;
  lab_manager_name: string;
  

  administrative_appointment: string;
  from_date: string;
  to_date: string;
  arrangement_made: string;
  no_administrative_appointment: string;             

  date: string;
  no_of_month: string;
  starting_term: string;
  ending_term: string;
  project_title: string;
  research_location: string;
  amount_requested: string;
  research_description: string;

totalfundrequestedParsed: string
  personnelParsed:PersonnelSchema[];
  travelParsed: TravelSchema[];
  equipmentParsed: OtherExpensesSchema[];
  suppliesParsed: OtherExpensesSchema[];
  otherexpensesParsed: OtherExpensesSchema[];

}

interface UserRequestViewerProps {
  userId: string;
}

export default function UserRequestViewer({
  userId,
}: UserRequestViewerProps) {
  const [loading, setLoading] = useState(false);
  const [filteredRequest, setFilteredRequest] = useState<SabbRequest[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  

    useEffect(() => {
      const fetchRequest = async () => {
        try {
          setLoading(true);
          const response = await fetch(`/api/sabatical/view-request?userRequest=${encodeURIComponent(userId)}`);
          if (response.ok) {
            const result = await response.json();
            

            const parsedData = result.data.map((item: any) => {
            let personnel = [];
            let travel = [];
            let equipment = [];
            let supplies = [];
            let otherexpenses = [];
            let totalfundrequested = '';
            try {
              personnel = JSON.parse(item.personnel || '[]');
            } catch (err) {
              console.error('Failed to parse personnel for item:', item.id, err);
            }
            try {
              travel = JSON.parse(item.travel || '[]');
            } catch (err) {
              console.error('Failed to parse travel for item:', item.id, err);
            }
            try {
              equipment = JSON.parse(item.equipment || '[]');
            } catch (err) {
              console.error('Failed to parse equipment for item:', item.id, err);
            }
            try {
              supplies = JSON.parse(item.supplies || '[]');
            } catch (err) {
              console.error('Failed to parse supplies for item:', item.id, err);
            }
            try {
              otherexpenses = JSON.parse(item.otherexpenses || '[]');
            } catch (err) {
              console.error('Failed to parse otherexpenses for item:', item.id, err);
            }
             try {
              totalfundrequested = JSON.parse(item.total_fund_requested || '');
              console.log(item.total_fund_requested);
            } catch (err) {
              console.error('Failed to parse otherexpenses for item:', item.id, err);
            }

            return {
              ...item,
              personnelParsed: personnel,
              travelParsed: travel,
              equipmentParsed: equipment,
              suppliesParsed: supplies,
              otherexpensesParsed: otherexpenses,
              totalfundrequestedParsed: totalfundrequested,
            };
          });

          setFilteredRequest(parsedData);

            //console.log("Fetched data:", result.data); // This is the array of reports

            if (result.data && result.data.length > 0) {
              const report = result.data[0];
              console.log("First report summary:", report);
            }

          } else {
            const error = await response.json();
            setError(error.error || 'Failed to fetch available roles');
          }
        } catch (error) {
          setError('An error occurred while fetching available report!');
          console.error(error);
        } finally {
          setLoading(false);
        }
      };

      fetchRequest();

    }, [userId]);

  return (
    <div className="space-y-4">

        {/* System Roles Section */}
            <div className="flex items-end gap-0">
             
                {filteredRequest.map((request) => (
                    <div  className=" top-0 left-0 flex flex-col w-full min-w-0 p-4 pl-0 pr-0 pb-8 break-wordsh-auto opacity-100 visible">

                        <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-3/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Name</span><br/>
                              {request.name}     
                            </div>
                            <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-5/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Department</span><br/>
                              {request.department}
                            </div>
                            <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-2/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Rank</span><br/>
                                {request.rank}
                            </div>
                            <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-2/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Tenure</span><br/>
                                {request.tenure}
                            </div>
                        </div>
                        <p className='md:mt-3 leading-normal text-sm'>First Appointed at University of Waterloo:</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                          <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                            <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Rank</span><br/>
                            {request.first_rank}
                          </div>
                          <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                            <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Date</span><br/>
                            {request.first_date.slice(0, 10)}
                          </div>
                        </div>
                        <p className='md:mt-3 leading-normal text-sm  font-bold'>Record of Past Leaves:</p>
                        {request.past_leaves?.map((item, index) => (
                            <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Select Leave Type</span><br/>
                               {item.type}
                            </div>
                            <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">From</span><br/>
                              {item.from}
                            </div>
                            <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">To</span><br/>
                              {item.to}
                            </div>
                            <div className="w-full max-w-full px-3 mb-3 flex-0 sm:mt-0 sm:w-3/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Salary Arrangement</span><br/>
                              {item.salaryArrangement}
                            </div>
                           
                          </div>
                          ))}

                          <p className='md:mt-3 leading-normal text-sm font-bold'>Current Request for Leave:</p>

                          {request.current_leaves?.map((item, index) => (
                            <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300" >
                            <div className="w-full max-w-full px-3 flex-0 mb-3 sm:w-3/12 ">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Select Leave Type</span><br/>
                               {item.type}
                            </div>
                            <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">From</span><br/>
                              {item.from}
                            </div>
                            <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">To</span><br/>
                              {item.to}
                            </div>
                            <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-3/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Salary Arrangement</span><br/>
                              {item.salaryArrangement}
                            </div>
                           
                          </div>
                          ))}

                          <p className='md:mt-3 leading-normal text-sm font-bold'>Non-teaching term(s): if leave is to be combined with non-teaching terms, specify non-teaching term dates below:</p>
                          {request.non_teach_term?.map((item, index) => (
                            <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300" >
                            <div className="w-full max-w-full px-3 flex-0 mb-3 sm:w-3/12 ">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Select Leave Type</span><br/>
                               {item.type}
                            </div>
                            <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">From</span><br/>
                              {item.from}
                            </div>
                            <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">To</span><br/>
                              {item.to}
                            </div>
                            <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-3/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Salary Arrangement</span><br/>
                              {item.salaryArrangement}
                            </div>
                           
                          </div>
                          ))}

                          <p className='md:mt-3 leading-normal text-sm font-semibold'>Outline of Leave: 60 words max. stating your area of research, plans for leave, and expected outcomes (for Board of Governors report)</p>
                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.leave_outline) }} />
                            </div>
                          </div>

                          <p className='md:mt-3 leading-normal text-sm font-semibold'>If the current request is partially based on credit for time spent in an administrative position, please specify post and dates</p>
                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.current_request_time_date) }} />
                            </div>
                          </div>

                          <p className='md:mt-3 leading-normal text-sm font-semibold'>Service Credit/Administrative Credit remaining</p>
                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.service_credit_remaining) }} />
                            </div>
                          </div>

                          <p className='md:mt-3 leading-normal text-sm font-semibold'>Are you requesting a grant in lieu of salary</p>
                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.grt_lieu_salary) }} />
                            </div>
                          </div>

                          <h4 className='font-bold pt-5 md:mt-3 z-10 mb-1 text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500 text-xl'>PLAN FOR SABBATICAL LEAVE</h4>
                          <p className='md:mt-3 leading-normal text-sm font-semibold'>Describe the value of your leave to the institution and to your professional development.</p>
                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.stp_2_leave_values) }} />
                            </div>
                          </div>

                          <p className='md:mt-3 leading-normal text-sm font-semibold'>Declaration of Research Funding (Research Office will be notified prior to Provost approval).</p>
                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.stp_2_hold_funding) }} />
                            </div>
                          </div>

                          <p className='md:mt-3 leading-normal text-sm font-semibold'>If yes, will all active Tri-Agency and/or Early Research Award (ERA) funded research projects continue during the leave?.</p>
                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.stp_2_era_funding) }} />
                            </div>
                          </div>
                          <p className='md:mt-3 leading-normal text-sm font-semibold'>If yes, is supervision in place for all students?</p>
                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.stp_2_era_supervision) }} />
                            </div>
                          </div>

                          <p className='md:mt-3 leading-normal text-sm font-semibold'>Declaration of Outside Employment or Fellowships</p>
                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.stp_2_out_rsrch_funding) }} />
                            </div>
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.stp_2_decl_out_emply) }} />
                            </div>
                          </div>

                          <p className='md:mt-3 leading-normal text-sm font-semibold'>If you have held previous sabbatical leaves, please attach a  report.</p>
                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">

                              {request.stp_2_pl_report && (
                                <a
                                  href={request.stp_2_pl_report}
                                  download="report.pdf"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 underline hover:text-blue-800"
                                >
                                  View Report
                                </a>
                              )}   
                            </div>
                          </div>

                          <h4 className='font-bold pt-5 md:mt-3 z-10 mb-1 text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500 text-xl'>GRADUATE STUDENT SUPERVISION DURING LEAVE OF ABSENCE</h4>
                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-6/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Department</span><br/>
                              {request.department} 
                            </div>
                            <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-6/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Applicant Name</span><br/>
                              {request.applicant_name} 
                            </div>
                          </div>

                          <div className="flex flex-wrap -mx-3 p-4 w-full ">
                            <p className='leading-normal text-sm w-full font-bold'>
                              During my proposed leave of absence
                            </p>
                          </div>
                          <div className="flex flex-wrap -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">From Date</span><br/>
                             {request.date_from.slice(0, 10)}
                            </div>
                            <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">To Date</span><br/>
                              {request.date_to.slice(0, 10)}
                            </div>
                          </div>

                          <div className="flex flex-wrap -mx-3 p-4 w-full font-bold">
                            <p className='leading-normal text-sm '>I have made the following arrangements for my graduate students: </p>
                          </div>
                          {request.delegated_supervisor?.map((item, index) => (
                                <div className="flex flex-wrap -mx-3">
                                <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                                  <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Student's Name</span><br/>
                                  {item.student_name} 
                                </div>
                                <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                                  <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Delegated Supervisor</span><br/>
                                  {item.supervisor}
                                </div>

                                </div>
                          ))}

                          <div className="flex flex-wrap -mx-3 mt-4 p-4 w-full ">
                            <h5 className="md:mt-3 font-bold  z-10 mb-1 text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500" >OR: Declaration of no graduate students during leave:</h5>
                        </div>

                        <div className="flex flex-wrap -mx-3">
                          <div className="w-full max-w-full px-3 flex-0 sm:w-8/12">
                            {request.no_grad_student && (
                              <label htmlFor='no_grad_student' className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">
                                I do not have and will not have any graduate students to supervise during my leave.
                              </label>
                            )}
                          </div>
                        </div>

                        <h4 className='font-bold pt-5 md:mt-3 z-10 mb-1 text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500 text-xl'>LABORATORY SAFETY SUPERVISION DURING LEAVE OF ABSENCE</h4> 

                        <p className='md:mt-3 leading-normal text-sm'>Absences of a nature/duration where aspects of physical laboratory supervision cannot be performed by the supervisor. This may include sabbaticals involving travel or extended absences (beyond one month) where the faculty member cannot attend the lab or adequately remotely oversee aspects of health and safety supervision. </p>

                        <p className="md:mt-3 font-bold  z-10 mb-1 text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500" >OR Please check applicable statement below:</p>
                        <div>
                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12 flex items-start">   
                                <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80"> If the absence is less than 1 month or where the faculty member remains available to fully perform health and safety supervision duties (in-person or remotely on-call). </span>     
                            </div>
                            <div className="w-full max-w-full px-3 p-3 flex-0 sm:w-12/12 flex items-start">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80 "> If permanent supervisory responsibility has been assigned to a competent employee lab manager
                              </span>   
                            </div>
                            <div className="w-full max-w-full px-3 p-3 flex-0 sm:w-12/12 flex items-start">
                                <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">If the faculty member does not have a lab.</span>
                               
                            </div>
                             <div className="w-full max-w-full px-3 flex-0 sm:w-12/12 flex items-start">
                               
                                {request.lab_responsibility }  
                            </div>
                          </div>
                          {request.lab_manager_name && (
                            <div className="flex flex-wrap mt-4 -mx-3">
                              <div className="w-full max-w-full px-3 mt-0 flex-0 sm:mt-0 sm:w-12/12">
                                <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Lab manager name.</span><br/>
                                {request.lab_manager_name}
                              </div>
                              <div className="w-full max-w-full px-3 mt-5 flex-0 sm:mt-5 sm:w-12/12">
                                <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Lab contact for emergency access.</span><br/>
                              {request.emergency_name} 
                              </div>
                            </div>
                          )}

                          <div className="flex flex-wrap mt-4 -mx-3 p-4">
                              <h5 className="mb-0 font-bold ">Roles & Responsibilities:</h5>
                              <p className='leading-normal text-sm w-full pt-5'>
                                Department Head
                              </p>
                                <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                                  <li className='leading-normal text-sm'>Ensure that an appropriate delegate supervisor has been identified during the leave.</li>
                                  <li className='leading-normal text-sm'>If, during the leave period, adequate supervision is not in place, cease operation of the research space.</li>
                                </ul>
                             
                              
                                <h5 className='leading-normal text-sm w-full pt-5'>Principal Investigator (Applicant)</h5>

                                <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                                  <li className='leading-normal text-sm'>To identify health and safety related supervisor activities requiring delegation during a leave of absence. These should include, at a minimum:
                                      <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                                      <li className='leading-normal text-sm'>Responding to incidents and emergencies and conducting incident investigations</li>
                                      <li className='leading-normal text-sm'>Conducting or overseeing monthly supervisory health and safety inspections</li>
                                      <li className='leading-normal text-sm'>Carrying out laboratory training and orientations</li>
                                      <li className='leading-normal text-sm'>Approving or providing written safe operating procedures</li>
                                      </ul>
                                  </li>
                                  <li className='leading-normal text-sm'>Prior to starting a leave, to provide the delegate supervisor an overview of:
                                      <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                                      <li className='leading-normal text-sm'>Processes, equipment, materials within the labs, including any hazards that the delegate must be aware of</li>
                                      <li className='leading-normal text-sm'>Rules established to ensure safe operation of equipment and processes, safe handling/disposal of materials</li>
                                      <li className='leading-normal text-sm'>Research members competencies and limitations, to ensure workers are provided the supports they need and are not required or allowed to perform work for which they are not competent.</li>
                                      <li className='leading-normal text-sm'>The Working Alone plan and its communication structure</li>
                                      </ul>

                                  </li>
                                  <li className='leading-normal text-sm'>To communicate to researchers, the identity and role of the delegate supervisor during the leave of absence and the expectations of research members.</li>
                                </ul>
                              
                              <p className='leading-normal text-sm w-full pt-5'>
                                Delegate Supervisor
                              </p>
                                <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                                  <li className='leading-normal text-sm'>To conduct required health and safety supervisory activities during the leave of absence period.</li>
                                  <li className='leading-normal text-sm'>To identify and address health and safety hazards, issues or concerns identified during the leave of absence period, or to escalate issues which are not under their purview to Department Head for resolution if necessary.</li>
                                
                                </ul>
                              
                          </div>

                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-6/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Name of Applicant</span><br/>
                              {request.applicant_name}
                                        
                            </div>
                            <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-6/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Building and Lab Room Number(s)</span><br/>
                              {request.lab_room_number}
                            </div>
                          </div>

                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-6/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Department</span><br/>
                              {request.dept_name}
                            </div>
                            <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-6/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Dates of Absence</span><br/>
                              {request.abscensce_date?.slice(0, 10)}
                            </div>
                          </div>
                          <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-6/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Delegate Supervisor Name</span><br/>
                              {request.delg_supervisor}
                            </div>
                            <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-6/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Department Head Name</span><br/>
                              {request.dept_head}
                            </div>
                          </div>
                        </div>

                        <h4 className='font-bold pt-5 md:mt-3 z-10 mb-1 text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500 text-xl'>ADMINISTRATIVE APPOINTMENT DURING LEAVE OF ABSENCE</h4>
                        
                        <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Applicant Name</span><br/>
                              {request.applicant_name}
                                        
                            </div>
                            <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Department</span><br/>
                             {request.dept_name}

                            </div>
                            <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Administrative Appointment</span><br/>
                              {request.administrative_appointment}
                            </div>
                        </div>
                        <p className='md:mt-3 leading-normal text-sm'>During my proposed leave of absence</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-6/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">From Dates</span><br/>
                              {request.from_date.slice(0,10)}
                            </div>
                            <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-6/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">To Date</span><br/>
                              {request.to_date.slice(0,10)}
                            </div>
                        </div>

                        <p className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500'>The following arrangements have been made for my Administrative Appointment(s):</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.arrangement_made) }} />
                            </div>
                        </div>
                        <p className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500'>OR: Declaration of no administrative appointments during leave</p>
                          
                        <div className="flex flex-wrap mt-4 -mx-3">
                              <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              {request.no_administrative_appointment && (
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">I do not have and will not hold an administrative appointment during my leave.</span> )}
                              </div>
                        </div>
                        <h4 className='font-bold pt-5 md:mt-3 z-10 mb-1 text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500 text-xl'>SABBATICAL RESEARCH GRANT IN LIEU OF REGULAR SALARY</h4>

                        <div className="flex flex-wrap mt-4 -mx-3">
                                  <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                                    <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Applicant Name</span><br/>
                                    {request.applicant_name}
                                  </div>
                                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                                    <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Department</span><br/>
                                    {request.department}
                                  </div>
                                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                                    <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Date</span><br/>
                                    {request.date?.slice(0,10)}
                                  </div>
                        </div>
                        <h2 className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500 '>A. BASIC INFORMATION</h2>

                        <p className='md:mt-3 leading-normal mb-2 ml-1 font-bold text-xs text-slate-700 /80'>1.	Indicate the Term of Research Leave Granted:: </p>

                        <div className="flex flex-wrap mt-2 -mx-3 " >
                          <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-4/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Starting</span><br/>
                                      {request.starting_term}
                          </div>
                          <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-4/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Ending</span><br/>
                                      {request.ending_term}
                          </div>
                          <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-4/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Number of Months</span><br/>
                                      {request.no_of_month}
                          </div>
                        </div>
                        <p className='md:mt-3 leading-normal mb-2 ml-1 font-bold text-xs text-slate-700 /80'>2.	Title of Research Project:</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                                {request.project_title}
                            </div>
                        </div>

                        <p className='md:mt-3 leading-normal mb-2 ml-1 font-bold text-xs text-slate-700 /80'>3.	Location of Research (Please name principal building or site where the research will be performed, for example, a college, hospital, main campus, or site in the Arctic):</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                                {request.research_location}
                            </div>
                        </div>
                        <p className='md:mt-3 leading-normal text-sm mb-2 ml-1 font-bold text-xs text-slate-700 /80'>4.	Total Amount Requested:</p>
                                
                        <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                                        {request.amount_requested}
                            </div>
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12 text-xs text-slate-500">
                                      Please note that T4A slips will be issued for the taxation year in which the installment payment is received, not the date on which the Payroll department receives the Research Grant Payment Form. (Installments will be  included  in  the  normal  salary  payments).  Visit  Revenue  Canada <a href="http://www.cra-arc.gc.ca/tx/tchncl/ncmtx/fls/s1/f2/s1-f2-c3-eng.html#N108BF">http://www.cra-arc.gc.ca/tx/tchncl/ncmtx/fls/s1/f2/s1-f2-c3-eng.html#N108BF</a> for further information.
                            </div>
                        </div>
                        <p className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500'>B.	DESCRIPTION OF PROPOSED RESEARCH</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              <span className="font-bold text-xs text-slate-700 /80" >Please give a description of the research objectives and procedures and a justification of the budget items listed under Section C and the choice of location(s), if any. Please note that the purpose and objects of the expenditures proposed must be warranted in the context of the research outlined. Applications that do not provide sufficient information will be returned.</span><br/>

                                    {request.research_description}
                              </div>
                        </div>

                        <p className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500'>C.	BUDGET</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                                    <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                                          <span className="font-bold text-xs text-slate-700 /80">Please give a description of the research objectives and procedures and a justification of the budget items listed under Section C and the choice of location(s), if any. Please note that the purpose and objects of the expenditures proposed must be warranted in the context of the research outlined. Applications that do not provide sufficient information will be returned.</span><br/>
                                        {request.amount_requested}
                                    </div>
                         </div>  
                          
                          <p className='md:mt-3 leading-normal text-sm font-semibold'>PERSONNEL:</p>
                                
                          {Array.isArray(request.personnelParsed) && request.personnelParsed.length > 0 ? (
                                request.personnelParsed.map((item: any, index: number) => (
                                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300  mb-5" key={index} >
                                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Name, position, and qualifications</span><br/>
                                      {item.name_position}
                                    </div>
                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Rate/Annum</span><br/>
                                      {item.rate_per_annum}
                                    </div>

                                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-1/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Hours/Week</span><br/>
                                      {item.hours_per_week} 
                                    </div>
                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Calc. Rate</span><br/>
                                      {item.calc_rate} 
                                    </div>
                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Fringe Benefits</span><br/>
                                     {item.fringe_benefit} 
                                    </div>
                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Estimated Expenses</span><br/>
                                      {item.est_expenses} 
                                    </div>
                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">$</span><br/>
                                      {item.cost} 
                                    </div>
                                  </div>
                            ))
                            ) : (
                              <p className="text-sm text-gray-500 italic">No personnel entries available.</p>
                            )}

                                <p className='md:mt-3 leading-normal text-sm '><b>TRAVEL AND RELATED COSTS FOR PRINCIPAL INVESTIGATOR</b> essential to research program (expense for sojourning and for spouse and family are not allowable) &nbsp;&nbsp;</p>

                                {Array.isArray(request.travelParsed) && request.travelParsed.length > 0 ? (
                                    request.travelParsed.map((item: any, index: number) => (
                                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300  mb-5" key={index} >
                                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Location(s)</span><br/>
                                      {item.location} 
                                    </div>

                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-3/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Duration</span><br/>
                                      {item.duration}
                                    </div>

                                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Travel Mode</span><br/>
                                      {item.travel_mode}
                                    </div>

                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Related Costs</span><br/>
                                     {item.related_cost} 
                                    </div>

                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">$</span><br/>
                                      {item.cost} 
                                    </div>

                                  </div>

                                ))
                                ) : (
                                  <p className="text-sm text-gray-500 italic">No personnel entries available.</p>
                                )}
                                
                                <p className='md:mt-3 leading-normal text-sm '><b>EQUIPMENT </b> (list specific items) &nbsp;&nbsp;</p>
                                {Array.isArray(request.equipmentParsed) && request.equipmentParsed.length > 0 ? (
                                  request.equipmentParsed.map((item: any, index: number) => (
                                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300  mb-5" key={index} >
                                    <div className="w-full max-w-full px-3 flex-0 sm:w-2/12 ">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Quantity</span><br/>
                                      {item.quantity} 
                                    </div>

                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-6/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Description</span><br/>
                                     {item.description}
                                    </div>

                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Unit Cost</span><br/>
                                      {item.unit_cost} 
                                    </div>

                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">$</span><br/>
                                      {item.cost}
                                    </div>
                                  </div>
                                  ))
                                ) : (
                                  <p className="text-sm text-gray-500 italic">No personnel entries available.</p>
                                )}

                                <p className='md:mt-3 leading-normal text-sm '><b>SUPPLIES </b> (list specific items) </p>
                                {Array.isArray(request.suppliesParsed) && request.suppliesParsed.length > 0 ? (
                                        request.suppliesParsed.map((item: any, index: number) => (
                                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300  mb-5" key={index} >
                                    <div className="w-full max-w-full px-3 flex-0 sm:w-2/12 ">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Quantity</span><br/>
                                      {item.quantity} 
                                    </div>
                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-6/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Description</span><br/>
                                      {item.description} 
                                    </div>
                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Unit Cost</span><br/>
                                      {item.unit_cost}
                                    </div>
                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">$</span><br/>
                                      {item.cost}
                                    </div>
                                  </div>
                                ))
                                ) : (
                                  <p className="text-sm text-gray-500 italic">No personnel entries available.</p>
                                )}


                                <p className='md:mt-3 leading-normal text-sm '><b>OTHER EXPENSES </b> (be specific) </p>
                                
                                {Array.isArray(request.otherexpensesParsed) && request.otherexpensesParsed.length > 0 ? (
                                  request.otherexpensesParsed.map((item: any, index: number) => (
                                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300 mb-5" key={index} >
                                    <div className="w-full max-w-full px-3 flex-0 sm:w-2/12 ">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Quantity</span><br/>
                                     {item.quantity}
                                    </div>
                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-6/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Description</span><br/>
                                      {item.description}
                                    </div>
                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Unit Cost</span><br/>
                                      {item.unit_cost}
                                    </div>
                                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">$</span><br/>
                                      {item.cost}
                                    </div>
                                  </div>
                                ))
                                ) : (
                                  <p className="text-sm text-gray-500 italic">No personnel entries available.</p>
                                )}

                                <div className="flex flex-wrap mt-4 -mx-3">
                                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12">
                                      <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Total Fund Requested</span><br/>
                                      {request.totalfundrequestedParsed}
                                    </div>
                                  </div>


                              </div>

                          ))}

              </div>
            
      {error && (
        <div className="text-sm text-red-500 mt-2">{error}</div>
      )}

      {success && (
        <div className="text-sm text-green-500 mt-2">{success}</div>
      )}

    </div>

  );
}
