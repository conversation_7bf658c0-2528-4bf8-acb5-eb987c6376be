'use client';

import { useState, useEffect } from 'react';
import DOMPurify from 'dompurify';
import { PlusIcon } from '@heroicons/react/24/outline';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Simple X icon component
const XIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <line x1="18" y1="6" x2="6" y2="18"></line>
    <line x1="6" y1="6" x2="18" y2="18"></line>
  </svg>
);

interface LeaveType {
  type: string;
  from: string;
  to: string;
  salaryArrangement: string;
}

interface SabbReport {
  appl_id:string;
  name: string;
  department: string;
  rank : string;
  tenure: string;
  first_rank: string;
  first_date: string;
  leave_outline: string;
  current_request_timedate: string;
  service_credit_remaining: string;
  grant_you_plan_to_apply: string;
  list_of_publication: string;
  subbatical_travel_purpose: string;
  research_project: string;
  involved_in_research: string;
  sabbatical_contribution: string;
  past_leaves?: LeaveType[];
  current_leaves?: LeaveType[];

}

interface UserReportManagerProps {
  userReport: string;
}

export default function UserReportManager({
  userReport,
}: UserReportManagerProps) {
  const [loading, setLoading] = useState(false);
  const [filteredReport, setFilteredReport] = useState<SabbReport[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  

    useEffect(() => {
      const fetchReport = async () => {
        try {
          setLoading(true);
          const response = await fetch(`/api/sabatical/view-report?userReport=${encodeURIComponent(userReport)}`);
          if (response.ok) {
            const result = await response.json();
            console.log(result);
            setFilteredReport(result.data);
            console.log("Fetched data:", result.data); // This is the array of reports

            if (result.data && result.data.length > 0) {
              const report = result.data[0];
              console.log("First report summary:", report);
            }

          } else {
            const error = await response.json();
            setError(error.error || 'Failed to fetch available roles');
          }
        } catch (error) {
          setError('An error occurred while fetching available report!');
          console.error(error);
        } finally {
          setLoading(false);
        }
      };

      fetchReport();

    }, [userReport]);

  return (
    <div className="space-y-4">

        {/* System Roles Section */}
            <div className="flex items-end gap-0">
             
                {filteredReport.map((request, index) => (
                    <div key={request.appl_id || index} className=" top-0 left-0 flex flex-col w-full min-w-0 p-4 pl-0 pr-0 pb-8 break-wordsh-auto opacity-100 visible">
                        
                        <div className="flex flex-wrap mt-4 -mx-3">
                          <div className="w-full max-w-full px-3 flex-0 sm:w-3/12">
                            <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Name</span><br/>
                            {request.name}     
                          </div>
                          <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-5/12">
                            <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Department</span><br/>
                            {request.department}
                          </div>
                          <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-2/12">
                            <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Rank</span><br/>
                              {request.rank}
                          </div>
                          <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-2/12">
                            <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Tenure</span><br/>
                              {request.tenure}
                          </div>
                        </div>

                        <p className='md:mt-3 leading-normal text-sm'>First Appointed at University of Waterloo:</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                          <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                            <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Rank</span><br/>
                            {request.first_rank}
                          </div>
                          <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                            <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Date</span><br/>
                            {request.first_date}
                          </div>
                        </div>

                        <p className='md:mt-3 leading-normal text-sm  font-bold'>Record of Past Leaves:</p>

                        {request.past_leaves?.map((item, index) => (
                            <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300">
                            <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Select Leave Type</span><br/>
                               {item.type}
                            </div>
                            <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">From</span><br/>
                              {item.from}
                            </div>
                            <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">To</span><br/>
                              {item.to}
                            </div>
                            <div className="w-full max-w-full px-3 mb-3 flex-0 sm:mt-0 sm:w-3/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Salary Arrangement</span><br/>
                              {item.salaryArrangement}
                            </div>
                           
                          </div>
                          ))}

                        <p className='md:mt-3 leading-normal text-sm font-bold'>Current Request for Leave:</p>

                        {request.current_leaves?.map((item, index) => (
                            <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300" >
                            <div className="w-full max-w-full px-3 flex-0 mb-3 sm:w-3/12 ">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Select Leave Type</span><br/>
                               {item.type}
                            </div>
                            <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">From</span><br/>
                              {item.from}
                            </div>
                            <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">To</span><br/>
                              {item.to}
                            </div>
                            <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-3/12">
                              <span className="mb-2 ml-1 font-bold text-xs text-slate-700 /80">Salary Arrangement</span><br/>
                              {item.salaryArrangement}
                            </div>
                           
                          </div>
                          ))}


                        <p className='md:mt-3 leading-normal text-sm font-semibold'>How did your sabbatical research contribute to the institution and to your professional development in research and/or teaching/pedagogy?</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                          <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                          
                           <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.sabbatical_contribution) }} />

                          </div>
                        </div>

                        <p className='md:mt-3 leading-normal text-sm font-semibold'>Who was involved in the research you carried out?</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                          <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                            
                            <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.involved_in_research) }} />

                          </div>
                        </div>

                        <p className='md:mt-3 leading-normal text-sm font-semibold'>Describe your research project(s), including how it/they aligned with or contributed to your longer-term research plans.</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                          <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                            <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.research_project) }} />

                          </div>
                        </div>

                        <p className='md:mt-3 leading-normal text-sm font-semibold'>When and where did you travel during your sabbatical and what was the purpose of your travel?</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                          <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                            <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.subbatical_travel_purpose) }} />
                           

                          </div>
                        </div>

                        <p className='md:mt-3 leading-normal text-sm font-semibold'>If applicable, which grants did you or do you plan to apply for as a result of your sabbatical research? Please provide details.</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                          <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                              
                          <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.grant_you_plan_to_apply) }} />

                          </div>
                        </div>

                        <p className='md:mt-3 leading-normal text-sm font-semibold'>List the publications, conference presentations, and any other forms of scholarly output that resulted from your sabbatical.</p>
                        <div className="flex flex-wrap mt-4 -mx-3">
                          <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                        
                            <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(request.list_of_publication) }} />

                          </div>
                        </div>

                          
                    </div>
                ))}

              </div>
            
     

      {error && (
        <div className="text-sm text-red-500 mt-2">{error}</div>
      )}

      {success && (
        <div className="text-sm text-green-500 mt-2">{success}</div>
      )}

    </div>

  );
}
