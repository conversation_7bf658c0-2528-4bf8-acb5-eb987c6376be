'use client';
import Link from 'next/link';
import { useForm, useField<PERSON><PERSON><PERSON>, Controller, useWatch } from 'react-hook-form';
import { useRouter } from "next/navigation";
import { useEffect, useState } from 'react';
import { calculateArrangement } from '@/app/lib/utils/calculateArrangement';
import QuillEditor from '@/components/ui/quill-editor'; // Import QuillEditor
import { isDate, parseISO, differenceInMonths, isValid } from 'date-fns';

type LeaveEntry = {
  type: string;
  from: string;
  to: string;
  salaryArrangement: string;
};

type TermEntry = {
  term: string;
  year: string;
};

type AdminAppts = {
  admin_title: string;
  admin_eff_from_dt: string;
  admin_eff_to_dt: string;
};

type RankInfo = {
  rank_id: string;
  rank_desc: string;
};

type LeaveType={
  leave_type:string;
}

type TenureType = {
  fac_type: string;
  fac_type_id: string;
};

type FormValues = {
  id: any,
  first_name:string;
  last_name:string;
  name: string;
  rank: string;
  department: string;
  tenure: string;
  first_rank: string;
  first_date: string;
  leaveOutline: string;
  leavetype:string;
  currentRequestTimeDate: string;
  serviceCreditRemaining: string;
  pastLeaves: LeaveEntry[];
  currentLeaves: LeaveEntry[];
  nonTeachTerm: TermEntry[];
  grtLieuSalary:string;
  adminAppointments: AdminAppts[];

};

export default function Form({ initialData = {} }: { initialData?: Partial<FormValues> & {
  rankInfo?: RankInfo[];
  leaveType?: LeaveType[];
  tenureType?: TenureType[];
  adminAppointments?: AdminAppts[];
};}) {

  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const router = useRouter();
  const [error, setError] = useState("");

  const [facultySsoId, setFacultySsoId] = useState<string | null>(null);

  const { register, control, handleSubmit, getValues, setValue} = useForm<FormValues>({
      defaultValues: {
      ...initialData,
      pastLeaves: [{ type: "", from: "", to: "", salaryArrangement: "" }],
      currentLeaves: [{ type: "", from: "", to: "", salaryArrangement: "" }],
      nonTeachTerm: [{ term: "", year: "" }],
      adminAppointments: [{ admin_title: "", admin_eff_from_dt: "", admin_eff_to_dt: "" }],

      leaveOutline: initialData?.leaveOutline || '',
      currentRequestTimeDate: initialData?.currentRequestTimeDate || '',
      serviceCreditRemaining: initialData?.serviceCreditRemaining || '',
      grtLieuSalary: initialData?.grtLieuSalary || '',
      rank: initialData.rank || '',
      leavetype: initialData.leavetype || '',
      first_name:initialData?.first_name || '',
      last_name:initialData?.last_name || '',
      tenure: initialData.tenure || '',

    },

  });

  const { fields: pastLeaveFields,
    append: appendPastLeave,
    remove: removePastLeave, } = useFieldArray({
    control,
    name: "pastLeaves",
  });

  const { fields: currentLeaveFields,
    append: appendCurrentLeave,
    remove: removeCurrentLeave, } = useFieldArray({
    control,
    name: "currentLeaves",
  });

  const { fields: adminAppointmentFields,
    append: appendAdminAppointment,
    remove: removeAdminAppointment, } = useFieldArray({
    control,
    name: "adminAppointments",
  });

  const { fields: nonTeachFields,
    append: appendNonTeachFields,
    remove: removeNonTeachFields, } = useFieldArray({
    control,
    name: "nonTeachTerm",
  });

  useEffect(() => {
    async function fetchSessionAndPastLeaves() {
      const sessionRes = await fetch("/api/auth/session");
      const session = await sessionRes.json();
      if (!session?.user?.facultySsoId) return;

      setFacultySsoId(session.user.facultySsoId);

      const res = await fetch(`/api/sabbaticalForm/get-Past-Leaves?id=${session.user.facultySsoId}`);
      const json = await res.json();

      if (json.success && Array.isArray(json.data)) {
        console.log(json.data);
        removePastLeave(); // Clear first
        json.data.forEach((item: LeaveEntry) => {
            appendPastLeave({
              type: item.type,
              from: item.from?.split("T")[0], // convert to YYYY-MM-DD
              to: item.to?.split("T")[0],
              salaryArrangement: item.salaryArrangement, // map correctly
          });
        });
      }
    }
    
    fetchSessionAndPastLeaves();
  }, [appendPastLeave, removePastLeave]);

    const onSubmit = async (data: FormValues) => {

      setLoading(true);
      setMessage("Submitting...");
      window.scrollTo(0, 0);
      document.body.scrollTop = 0;
      document.documentElement.scrollTop = 0;
      
      try {
        
        const response = await fetch('/api/sabbaticalForm/sabbatical-request-form', {
          method: 'POST',
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        });

        const res = await response.json();
          if (res.success) {
            setMessage("Submitted successfully!");
            router.push('/dashboard/sabbatical/sabbatical-step2?message=Request+submitted+Complete+Step+2&status=success');
          } else {
            setMessage(res.error || "Something went wrong.");
            //setError(res.error || 'Submission failed.');
          }
        } catch (err) {
          setMessage("Unexpected error occurred.");
          //setError('Unexpected error occurred.');
        }

    };

  const pastLeaves = useWatch({ control, name: 'pastLeaves' });
  const currentFrom = useWatch({ control, name: 'currentLeaves.0.from' });
  const currentTo = useWatch({ control, name: 'currentLeaves.0.to' });
  const firstDate = useWatch({ control, name: 'first_date' });
  const nonTeachTerm = useWatch({ control, name: 'nonTeachTerm' });
  const adminAppointments = useWatch({ control, name: 'adminAppointments' });
  const tenure = useWatch({ control, name: 'tenure' });


  const hasAdminCredits = Array.isArray(pastLeaves) &&  pastLeaves.some(leave => leave?.type?.toLowerCase() === "administrative");

  const eligibleTitles = [
    "Chair",
    "Associate Dean",
    "School Director",
    "Associate Chair",
    "Executive Director",
    "Academic Director",
    "Dean",
  ].map(title => title.toLowerCase());

  useEffect(() => {
    if (hasAdminCredits == true) {
      setValue("serviceCreditRemaining", "0");
    }else{

      if (!Array.isArray(initialData.adminAppointments)) return;
      let totalMonths = 0;

      initialData.adminAppointments.forEach((appt) => {

        const title = appt.admin_title?.trim().toLowerCase();
        console.log(title);
        if ( title && eligibleTitles.includes(title)) {
            const from = isDate(appt.admin_eff_from_dt) ? appt.admin_eff_from_dt : parseISO(appt.admin_eff_from_dt);
            const to = isDate(appt.admin_eff_to_dt) ? appt.admin_eff_to_dt : parseISO(appt.admin_eff_to_dt)
            console.log(from);

            if (isValid(from) && isValid(to)) {
              const today = new Date();
              const effectiveTo = to > today ? today : to;

              if (effectiveTo > from) {
                const months = differenceInMonths(effectiveTo, from);
                totalMonths += Math.max(months, 0);
              }
            } 
            
        }
      });
      
      const totalCredits = Math.floor(totalMonths / 12); // 1 credit per 12 months
      setValue('serviceCreditRemaining', totalCredits.toString());

    }
  }, [hasAdminCredits, setValue]);



  useEffect(() => {
    if (currentFrom && currentTo) {
      const value = calculateArrangement(pastLeaves, currentFrom, currentTo, nonTeachTerm, adminAppointments, firstDate, tenure);
      setValue('currentLeaves.0.salaryArrangement', value);
    }
  }, [pastLeaves, currentFrom, currentTo, nonTeachTerm, adminAppointments, firstDate, tenure]);

  return (
          <div className='w-full max-w-full px-5 m-auto lg:w-11/12 '>

          {message && <div className="mb-4"><div className="bg-blue-100 text-blue-700 p-3 rounded">{message}</div></div>}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 relative mb-32" >
            <div  className=" top-0 left-0 flex flex-col w-full min-w-0 p-4 pl-8 pr-8 pb-8 break-words bg-white border-0 shadow-soft-xl rounded-2xl bg-clip-border h-auto opacity-100 visible">

                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-3/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700" htmlFor="delg_supervisor">Name</label>
                    <input type="text" {...register("name", { required: true })} defaultValue={initialData.name} placeholder="Name" id="name" name="name" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" disabled/>

                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-3/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700" htmlFor="dept_head">Department</label>
                    <input disabled type="text" {...register("department", { required: true })} defaultValue={initialData.department} placeholder="Department" id="department" name="department" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`

                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-3/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Rank</label>

                    {/* Rank Dropdown */}
                    <select {...register("rank", { required: true })} defaultValue={initialData.rank || ""} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" disabled>
                      <option key="select-rank" value="" >Select Rank</option>
                      {initialData.rankInfo?.map((rank, index) => (
                        <option key={index} value={rank.rank_id}>
                          {rank.rank_desc}
                        </option>
                      ))}
                    </select>

                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-3/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Tenure </label>

                      <select {...register("tenure", { required: true })} defaultValue={initialData.tenure || ""} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" disabled>
                      <option key="select-tenure" value="">Select Tenure</option>
                      {initialData.tenureType?.map((tenure) => (
                        <option key={tenure.fac_type_id} value={tenure.fac_type}>
                          {tenure.fac_type}
                        </option>
                      ))}
                    </select>

                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm font-bold'>First Appointed at University of Waterloo:</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_rank">Rank</label>
                    {/* Rank Dropdown */}
                    <select {...register("first_rank", { required: true })} defaultValue={initialData.first_rank || ""} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" disabled>
                      <option key="select-first-rank" value="">Select Rank</option>
                      {initialData.rankInfo?.map((rank, index) => (
                        <option key={index} value={rank.rank_id}>
                          {rank.rank_desc}
                        </option>
                      ))}
                    </select>

                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_date">Date </label>
                    <input {...register("first_date", { required: true })}  placeholder="first_date"
                    id="first_date"
                    name="first_date"
                    type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" disabled/>
                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm  font-bold'>Record of Past Leaves: <button type="button" onClick={() => appendPastLeave({ type: "", from: "", to: "", salaryArrangement: "" })} className='text-blue-700'> + Add Entry
                </button></p>

                {pastLeaveFields.map((field, index) => (
                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300" key={field.id} >
                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_rank">Select Leave Type</label>

                        <select {...register(`pastLeaves.${index}.type`)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                        <option key="select-past-leave-type" value="">Select Leave Type</option>
                        {initialData.leaveType?.map((leaves, index) => (
                           <option key={index} value={leaves.leave_type}>
                           {leaves.leave_type}
                         </option>
                        ))}
                      </select>

                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">From</label>
                      <input
                      {...register(`pastLeaves.${index}.from`)} placeholder="From"
                      id="from"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">To</label>
                      <input
                      {...register(`pastLeaves.${index}.to`)} placeholder="To"
                      id="to"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-4/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Salary Arrangement</label>
                      
                      <input {...register(`pastLeaves.${index}.salaryArrangement`)} type="text" placeholder="Salary Arrangement"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>


                    </div>
                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => removePastLeave(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>

                  </div>

                ))}

                <p className='md:mt-3 leading-normal text-sm font-bold'>Current Request for Leave: {/* <button type="button" onClick={() => appendCurrentLeave({ type: "", from: "", to: "", salaryArrangement: "" })} className='text-blue-700'> + Add Entry
                </button>*/}</p>

                {currentLeaveFields.map((field, index) => (
                  <div className="pb-4 flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300" key={field.id} >

                    <div className=" w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Select Leave Type</label>

                      <select {...register(`currentLeaves.${index}.type`)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" required>
                        <option key="select-current-leave-type" value="">Select Leave Type</option>
                        {initialData.leaveType?.map((leaves, index) => (
                           <option key={index} value={leaves.leave_type}>
                           {leaves.leave_type}
                         </option>
                        ))}
                      </select>


                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">From</label>
                        <input
                        {...register(`currentLeaves.${index}.from`)}
                      id="from"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" required />
                    </div>
                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">To</label>
                      <input
                      {...register(`currentLeaves.${index}.to`)}
                      id="to"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" required/>
                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-4/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Salary Arrangement in %</label>
                        <Controller
                          name={`currentLeaves.${index}.salaryArrangement`}
                          control={control}
                          render={({ field }) => (
                            <input readOnly {...field} placeholder="Salary Arrangement" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" />
                          )}
                        />
                     

                    </div>
                    {/*  <input {...register(`currentLeaves.${index}.salaryArrangement`)} placeholder="Salary Arrangement"
                     className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                     
                     
                     <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => removeCurrentLeave(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div> */}

                  </div>

                ))}

                <p className='md:mt-3 leading-normal text-sm font-bold'>Non-teaching term(s): if leave is to be combined with non-teaching terms, specify non-teaching term dates below. <button type="button" onClick={() => appendNonTeachFields({ term: "", year: "" })} className='text-blue-700'> + Add Entry </button> </p>

                {nonTeachFields.map((field, index) => (
                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300" key={field.id}>
                    <div className="pb-4 w-full max-w-full px-3 flex-0 sm:w-3/12 " >
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_rank">Select Term</label>

                      <select {...register(`nonTeachTerm.${index}.term`)} className="focus:shadow-soft-primary-outline /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                        <option key="select-non-teach-term" value="">Select Term</option>
                           <option value="Spring">Spring</option>
                           <option value="Fall">Fall</option>
                           <option value="Winter">Winter</option>
                      </select>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="year">Year</label>
                      <select
                        {...register(`nonTeachTerm.${index}.year`)}
                        id="year"
                        className="focus:shadow-soft-primary-outline /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"
                      >
                        <option value="">Select Year</option>
                        {Array.from({ length: 6 }, (_, i) => {
                          const year = new Date().getFullYear() + i;
                          return (
                            <option key={year} value={year}>
                              {year}
                            </option>
                          );
                        })}
                      </select>


                    </div>
                    {/* 
                      <input
                      {...register(`nonTeachTerm.${index}.year`)}
                      id="year"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                      
                      <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">To</label>
                      <input
                      {...register(`nonTeachTerm.${index}.to`)}
                      id="to"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                   <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-4/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Salary Arrangement</label>
                      <input {...register(`nonTeachTerm.${index}.salaryArrangement`)} placeholder="Salary Arrangement"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" readOnly/>
                    </div>*/}
                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => removeNonTeachFields(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>
                  </div>

                ))}

                <p className='md:mt-3 leading-normal text-sm'>Outline of Leave: 60 words max. stating your area of research, plans for leave, and expected outcomes (for Board of Governors report)</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">

                  <QuillEditor
                    name="leaveOutline"
                    control={control}
                    placeholder="Outline of Leave: 60 words max. stating your area of research, plans for leave, and expected outcomes (for Board of Governors report)"
                    defaultValue={initialData.leaveOutline || ''}
                  />
                  </div>

                </div>

                <p className='md:mt-3 leading-normal text-sm'>If the current request is partially based on credit for time spent in an administrative position, please specify post and dates <button type="button" onClick={() => appendAdminAppointment({ admin_title: "",  admin_eff_from_dt: "",  admin_eff_to_dt: "" })} className='text-blue-700'> + Add Entry </button></p>
                {adminAppointmentFields.map((field, index) => (
                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300" key={field.id} >
                    <div className="w-full max-w-full px-3 flex-0 sm:w-4/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_rank">Administrative Position Held</label>

                        <select  className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" onChange={(e) => {
                        //const selectedTitle = e.target.value;
                        //const selectedObj = initialData.adminAppointments?.find(item => item.admin_title === selectedTitle);

                        const selectedIndex = parseInt(e.target.value, 10);
                        const selectedObj = initialData.adminAppointments?.[selectedIndex];

                        if (selectedObj) {
                          setValue(`adminAppointments.${index}.admin_title`, selectedObj.admin_title);
                          setValue(`adminAppointments.${index}.admin_eff_from_dt`, new Date(selectedObj.admin_eff_from_dt).toLocaleDateString('en-CA'));
                          setValue(`adminAppointments.${index}.admin_eff_to_dt`, selectedObj.admin_eff_to_dt? new Date(selectedObj.admin_eff_to_dt).toLocaleDateString('en-CA') : '');
                        }
                      }}
                      defaultValue="">
                        <option key="select-administrative-position" value="">Administrative Position Held</option>
                       
                        {initialData.adminAppointments?.map((title, index) => {
                          const from = new Date(title.admin_eff_from_dt).toLocaleDateString('en-CA'); // YYYY-MM-DD
                          const to = title.admin_eff_to_dt ? new Date(title.admin_eff_to_dt).toLocaleDateString('en-CA') : 'Present';
                          const key = `${title.admin_title}-${from}-${to}`;
                          return (
                            <option key={key} value={index}>
                              
                              {title.admin_title} ({from} - {to}) 
                            </option>
                          );
                        })}

                      </select>

                        
                      <input type="hidden" {...register(`adminAppointments.${index}.admin_title`)}  />
                      <input type="hidden" {...register(`adminAppointments.${index}.admin_eff_from_dt`)}  />
                      <input type="hidden" {...register(`adminAppointments.${index}.admin_eff_to_dt`)}  />

                    </div>

                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => removeAdminAppointment(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>

                  </div>

                ))}
                
{/* {...register(`adminAppointments.${index}.admin_title`)}
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">

                  <QuillEditor
                    name="currentRequestTimeDate"
                    control={control}
                    placeholder="If the current request is partially based on credit for time spent in an administrative position, please specify post and dates"
                    defaultValue={initialData.currentRequestTimeDate || ''}
                  />

                  </div>
                </div>
*/}
                <p className='md:mt-3 leading-normal text-sm'>Service Credit/Administrative Credit remaining (in months)</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">

                  <input {...register("serviceCreditRemaining")}  placeholder="Service Credit/Administrative Credit remaining"
                    id="serviceCreditRemaining"
                    name="serviceCreditRemaining"
                    type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" />


                  {/*<QuillEditor
                    name="serviceCreditRemaining"
                    control={control}
                    placeholder="Service Credit/Administrative Credit remaining"
                    defaultValue={initialData.serviceCreditRemaining || ''}
                  />*/}
                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm'>Service Credit/Administrative Credit remaining</p>

                    <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                    <li className='leading-normal text-sm'><strong>Please complete Step 2:</strong> declaration of any outside employment or fellowship; declaration of any external grant funding; travel declaration</li>
                    <li className='leading-normal text-sm'><strong>Please complete Step 3:</strong> Graduate Student Supervision During Leave of Absence form </li>
                    <li className='leading-normal text-sm'><strong>Please complete Step 4:</strong> Laboratory Safety Supervision During Leave of Absence form </li>
                    <li className='leading-normal text-sm'><strong>Please complete Step 5:</strong> Administrative Appointment During Leave of Absence form </li>
                    <li className='leading-normal text-sm'><strong>Are you requesting a grant in lieu of salary?</strong>
                        <div className="flex flex-wrap mt-4 -mx-3 mb-4">
                          <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                          <input type='radio' id="yes" {...register("grtLieuSalary", { required: true })} value='Yes' className='mr-3'/> <label htmlFor='yes' className='mr-4'>Yes</label>
                          <input type='radio' id="no" {...register("grtLieuSalary")} value='No'  className='mr-3' /> <label htmlFor='no' className='mr-4'>No</label>
                          </div>
                        </div>

                     </li>
                    <li className='leading-normal text-sm'>Please complete if applicable Step 6:  application for Research Grant.</li>
                    </ul>

                    <div className="flex justify-end mt-6">
        

                     <button
                        type="submit"
                        disabled={loading}
                        className={`inline-block px-6 py-3 m-0 ml-2 text-xs font-bold text-center text-white uppercase align-middle transition-all border-0 rounded-lg cursor-pointer bg-150 bg-x-25   ${
                          loading ? 'bg-gray-400 cursor-not-allowed' : 'ease-soft-in leading-pro tracking-tight-soft bg-gradient-to-tl from-purple-700 to-pink-500 shadow-soft-md hover:scale-102 active:opacity-85'
                        } text-white`}
                      >
                      {loading ? '   Processing...   ' : 'Save and Continue'}
                       </button>

                    </div>

            </div>

          </form>

    </div>
  );
}


