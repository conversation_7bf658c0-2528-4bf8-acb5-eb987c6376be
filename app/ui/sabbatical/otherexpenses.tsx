import { useFormContext, useWatch, Controller } from "react-hook-form";
import { useEffect } from "react";

const OtherExpenseRow = ({ index, remove }: { index: number; remove: (index: number) => void }) => {
  const { control, register, setValue } = useFormContext();

  const quantity = useWatch({ control, name: `otherexpenses.${index}.quantity` });
  const unitCost = useWatch({ control, name: `otherexpenses.${index}.unit_cost` });

  const calculatedCost = (Number(quantity) || 0) * (Number(unitCost) || 0);

  useEffect(() => {
    setValue(`otherexpenses.${index}.cost`, calculatedCost.toFixed(2));
  }, [quantity, unitCost, index, setValue]);

  return (
    <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300 mb-5">
      <div className="w-full max-w-full px-3 flex-0 sm:w-2/12 ">
        <label htmlFor={`quantity-${index}`}>Quantity</label>
        <input
          {...register(`otherexpenses.${index}.quantity`)}
          id={`quantity-${index}`}
          type="number"
          className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 pl-4  py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"
        />
      </div>

      <div className="w-full max-w-full px-3 flex-0 sm:w-6/12">
        <label htmlFor={`description-${index}`}>Description</label>
        <input
          {...register(`otherexpenses.${index}.description`)}
          id={`description-${index}`}
          type="text"
          className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 pl-4  py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"
        />
      </div>

      <div className="w-full max-w-full px-3 flex-0 sm:w-2/12">
        <label htmlFor={`unit_cost-${index}`}>Unit Cost</label>
        <input
          {...register(`otherexpenses.${index}.unit_cost`)}
          id={`unit_cost-${index}`}
          type="number" step="0.01"
         className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 pl-4  py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"
        />
      </div>

      <div className="w-full max-w-full px-3 flex-0 sm:w-1/12">
        <label htmlFor={`cost-${index}`}>$</label>
        <input
          {...register(`otherexpenses.${index}.cost`)}
          id={`cost-${index}`}
          type="number" step="0.01"
          className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 pl-4  py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"
          readOnly
          value={calculatedCost.toFixed(2)}
        />
      </div>

      <div className="p-4 rounded sm:w-1/12">
        <button type="button" onClick={() => remove(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
          X
        </button>
      </div>
    </div>
  );
};

export default OtherExpenseRow;