'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function SuccessMessage() {
  const searchParams = useSearchParams();
  const [message, setMessage] = useState('');
  const [visible, setVisible] = useState(true);

  const status = searchParams.get('status');

  useEffect(() => {
    const msg = searchParams.get('message');
    if (msg) {
      setMessage(msg);
      setVisible(true);
      window.scrollTo(0, 0); // Scroll to top when message is present
    }
  }, [searchParams]);

  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setVisible(false), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  if (!message || !visible) return null;

  return (
    <div
      className={`p-4 m-auto w-full rounded-md mb-4 text-sm md:w-11/12 font-medium transition-opacity duration-1000 ${
        status === 'success'
          ? 'bg-green-100 text-green-700'
          : 'bg-red-100 text-red-700'
      }`}
    >
      {message}
    </div>
  );
}