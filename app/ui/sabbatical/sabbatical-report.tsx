'use client';
import Link from 'next/link';
import { useForm, useField<PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from '@/app/ui/button';
import { useState } from "react";
import QuillEditor from '@/components/ui/quill-editor'; // Import QuillEditor

type RankInfo = {
  rank_id: string;
  rank_desc: string;
};

type LeaveType={
  leave_type:string;
}

type TenureType = {
  fac_type: string;
  fac_type_id: string;
};

type LeaveEntry = {
  type: string;
  from: string;
  to: string;
  salaryArrangement: string;
};

type FormValues = {

  first_name:string;
  last_name:string;
  name: string;
  department: string;
  rank: string;
  tenure: string;
  first_rank: string;
  first_date: string;
  appl_id: string;
  leaveOutline: string;
  currentRequestTimeDate: string;
  serviceCreditRemaining: string;
  grantYouPlanToApply: string;
  listOfPublication: string;
  subbaticalTravelPurpose: string;
  researchProject: string;
  involvedInResearch: string;
  sabbaticalContribution: string;
  leavetype:string;
  pastLeaves: LeaveEntry[];
  currentLeaves: LeaveEntry[];

};

export default function Form({ initialData = {} }: { initialData?: Partial<FormValues> & {
  rankInfo?: RankInfo[];
  leaveType?: LeaveType[];
  tenureType?: TenureType[];
};}) {

  const searchParams = useSearchParams();
  const userId = searchParams.get('id'); // will return the value of ?id=

  const router = useRouter();
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const { register, control, handleSubmit, getValues } = useForm<FormValues>({
    defaultValues: {
      ...initialData,
      pastLeaves: [{ type: "", from: "", to: "", salaryArrangement: "" }],
      currentLeaves: [{ type: "", from: "", to: "", salaryArrangement: "" }],
      first_name:initialData?.first_name || '',
      last_name:initialData?.last_name || '',
      name: initialData?.name || '',
      leavetype: initialData.leavetype || '',
    },
  });

  const { fields: pastLeaveFields,
    append: appendPastLeave,
    remove: removePastLeave, } = useFieldArray({
    control,
    name: "pastLeaves",
  });

  const { fields: currentLeaveFields,
    append: appendCurrentLeave,
    remove: removeCurrentLeave, } = useFieldArray({
    control,
    name: "currentLeaves",
  });

  const onSubmit = async (data: FormValues) => {
    
    setLoading(true);
    setMessage("Submitting...");
    window.scrollTo({ top: 0, behavior: 'smooth' });

    try {
      const response = await fetch('/api/sabbaticalForm/sabbatical-report', {
        method: 'POST',
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      const res = await response.json();
      if (res.success) {
        setMessage("Submitted successfully!");
        router.push('/dashboard/sabbatical/view-approved-list?message=Request+submitted&status=success');
      } else {
        
        setMessage(res.error || "Something went wrong.");
        //setError(res.error || 'Submission failed.');
      }
    } catch (err) {
      setMessage("Unexpected error occurred.");
      //setError('Unexpected error occurred.');
    }
  };

  return (
    <div className='w-full max-w-full px-5 m-auto lg:w-11/12 '>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 relative mb-32" >
          {message && <div className="mb-4"><div className="bg-blue-100 text-blue-700 p-3 rounded">{message}</div></div>}

            <div  className=" top-0 left-0 flex flex-col w-full min-w-0 p-4 pl-8 pr-8 pb-8 break-words bg-white border-0   shadow-soft-xl rounded-2xl bg-clip-border h-auto opacity-100 visible">
                
                <input type="hidden" {...register("appl_id", { required: true })} defaultValue={userId ?? ''} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" readOnly/>

                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-3/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="delg_supervisor">Name</label>
                    <input type="text" {...register("name", { required: true })} defaultValue={initialData.name} placeholder="Name" id="name" name="name" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                              
                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-3/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Department</label>
                    <input type="text" {...register("department", { required: true })} defaultValue={initialData.department} placeholder="Department" id="department" name="department" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`

                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-3/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Rank</label>
                     <select {...register("rank", { required: true })} defaultValue={initialData.rank || ""} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                      <option key="select-rank" value="" >Select Rank</option>
                      {initialData.rankInfo?.map((rank) => (
                        <option key={rank.rank_id} value={rank.rank_id}>
                          {rank.rank_desc}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-3/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Tenure</label>
                      <select {...register("tenure", { required: true })} defaultValue={initialData.tenure || ""} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                        <option key="select-tenure" value="">Select Tenure</option>
                        {initialData.tenureType?.map((tenure) => (
                          <option key={tenure.fac_type_id} value={tenure.fac_type}>
                            {tenure.fac_type}
                          </option>
                        ))}
                      </select>
                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm'>First Appointed at University of Waterloo:</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_rank">Rank</label>
                    <select {...register("first_rank", { required: true })} defaultValue={initialData.rank || ""} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                      <option key="select-first-rank" value="">Select Rank</option>
                      {initialData.rankInfo?.map((rank) => (
                        <option key={rank.rank_id} value={rank.rank_id}>
                          {rank.rank_desc}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Date</label>
                    <input {...register("first_date", { required: true })} defaultValue={initialData.first_date} placeholder="Tenure"
                    id="first_date"
                    name="first_date"
                    type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm  font-bold'>Record of Past Leaves: <button type="button" onClick={() => appendPastLeave({ type: "", from: "", to: "", salaryArrangement: "" })} className='text-blue-700'> + Add Entry
                </button></p>
                 
                {pastLeaveFields.map((field, index) => (
                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300" key={field.id} >
                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                     <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_rank">Select Leave Type</label>

                        <select {...register(`pastLeaves.${index}.type`)} defaultValue={initialData.leavetype || ""} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                        <option key="select-past-leave-type" value="">Select Leave Type</option>
                        {initialData.leaveType?.map((leaves, index) => (
                           <option key={index} value={leaves.leave_type}>
                           {leaves.leave_type}
                         </option>
                        ))}
                      </select>

                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">From</label>
                      <input
                      {...register(`pastLeaves.${index}.from`)} placeholder="From"
                      id="from"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">To</label>
                     <input
                      {...register(`pastLeaves.${index}.to`)} placeholder="To"
                      id="to"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-3/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Salary Arrangement</label>
                      <input {...register(`pastLeaves.${index}.salaryArrangement`)} placeholder="Salary Arrangement"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>

                    </div>
                    <div className="p-4 rounded  sm:w-1/12">
                       <button type="button" onClick={() => removePastLeave(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>
                  
                  </div>

                ))}
            
               
                <p className='md:mt-3 leading-normal text-sm font-bold'>Current Request for Leave: <button type="button" onClick={() => appendCurrentLeave({ type: "", from: "", to: "", salaryArrangement: "" })} className='text-blue-700'> + Add Entry
                </button></p>

                 
                {currentLeaveFields.map((field, index) => (
                  <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300" key={field.id} >
                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" >Select Leave Type</label>

                      <select {...register(`currentLeaves.${index}.type`)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                        <option key="select-current-leave-type" value="">Select Leave Type</option>
                        {initialData.leaveType?.map((leaves, index) => (
                           <option key={index} value={leaves.leave_type}>
                           {leaves.leave_type}
                         </option>
                        ))}
                      </select>


                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">From</label>
                        <input
                        {...register(`currentLeaves.${index}.from`)}
                      id="from"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">To</label>
                      <input
                      {...register(`currentLeaves.${index}.to`)}
                      id="to"
                      type="date" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-3/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Salary Arrangement</label>
                      <input {...register(`currentLeaves.${index}.salaryArrangement`)} placeholder="Salary Arrangement"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>

                    </div>
                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => removeCurrentLeave(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>

                  </div>

                ))}


                <p className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500'>Within one month of the conclusion of your sabbatical, you are required to submit to your Chair/Director a report on your sabbatical leave. The report must answer the following questions: <br/><br/>This report will be considered as part of your next Faculty Performance Evaluation and to assess your next sabbatical leave application.</p>

                <p className='md:mt-3 leading-normal text-sm font-semibold'>How did your sabbatical research contribute to the institution and to your professional development in research and/or teaching/pedagogy?</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                  
                  <QuillEditor
                      
                      name="sabbaticalContribution"
                      control={control}
                      placeholder="How did your sabbatical research contribute to the institution and to your professional development in research and/or teaching/pedagogy?"
                      defaultValue={initialData.sabbaticalContribution || ''}
                    />

                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm font-semibold'>Who was involved in the research you carried out?</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                    <QuillEditor
                      
                      name="involvedInResearch"
                      control={control}
                      placeholder="Who was involved in the research you carried out?"
                      defaultValue={initialData.involvedInResearch || ''}
                    />

                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm font-semibold'>Describe your research project(s), including how it/they aligned with or contributed to your longer-term research plans.</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                  <QuillEditor
                      
                      name="researchProject"
                      control={control}
                      placeholder="Describe your research project(s), including how it/they aligned with or contributed to your longer-term research plans."
                      defaultValue={initialData.researchProject || ''}
                    />


                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm font-semibold'>When and where did you travel during your sabbatical and what was the purpose of your travel?</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">

                  <QuillEditor
                  
                      name="subbaticalTravelPurpose"
                      control={control}
                      placeholder="When and where did you travel during your sabbatical and what was the purpose of your travel?"
                      defaultValue={initialData.subbaticalTravelPurpose || ''}
                    />


                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm font-semibold'>If applicable, which grants did you or do you plan to apply for as a result of your sabbatical research? Please provide details.</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">

                  <QuillEditor
                      
                      name="grantYouPlanToApply"
                      control={control}
                      placeholder="If applicable, which grants did you or do you plan to apply for as a result of your sabbatical research? Please provide details."
                      defaultValue={initialData.grantYouPlanToApply || ''}
                    />


                  </div>
                </div>

                <p className='md:mt-3 leading-normal text-sm font-semibold'>List the publications, conference presentations, and any other forms of scholarly output that resulted from your sabbatical.</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                    <QuillEditor
                       
                        name="listOfPublication"
                        control={control}
                        placeholder="List the publications, conference presentations, and any other forms of scholarly output that resulted from your sabbatical."
                        defaultValue={initialData.listOfPublication || ''}

                        className="min-h-[200px]"

                      />

                  </div>
                </div>

                    <div className="flex justify-end mt-6">

                      <button
                        type="submit"
                        disabled={loading}
                        className={`inline-block px-6 py-3 m-0 ml-2 text-xs font-bold text-center text-white uppercase align-middle transition-all border-0 rounded-lg cursor-pointer bg-150 bg-x-25   ${
                          loading ? 'bg-gray-400 cursor-not-allowed' : 'ease-soft-in leading-pro tracking-tight-soft bg-gradient-to-tl from-purple-700 to-pink-500 shadow-soft-md hover:scale-102 active:opacity-85'
                        } text-white`}
                      >
                      {loading ? '   Processing...   ' : 'Submit'}
                    </button>

                    </div>

            </div>

          </form>
      
    </div>
  );
}
        
      
