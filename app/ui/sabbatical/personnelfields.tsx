import { useFormContext, useWatch, Controller } from "react-hook-form";
import { useEffect } from "react";

const PersonnelFieldsRow = ({ index, remove }: { index: number; remove: (index: number) => void }) => {
  const { control, register, setValue } = useFormContext();

  const hoursWeek = useWatch({ control, name: `personnel.${index}.hours_per_week` });
  const rateAnnum = useWatch({ control, name: `personnel.${index}.rate_per_annum` });
  const fringeBenefit = useWatch({ control, name: `personnel.${index}.fringe_benefit` });

  const calculatedRate = (Number(rateAnnum) || 0) / ((Number(hoursWeek) || 0) * 52);
  const calculatedExpenses = (Number(rateAnnum) || 0) + ((Number(rateAnnum) * Number(fringeBenefit) || 0)/100);

  useEffect(() => {
    setValue(`personnel.${index}.calc_rate`, calculatedRate.toFixed(2));

    setValue(`personnel.${index}.est_expenses`, calculatedExpenses.toFixed(2));

    setValue(`personnel.${index}.cost`, calculatedExpenses.toFixed(2));

  }, [hoursWeek, rateAnnum, fringeBenefit, index, setValue]);

  return (
                <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300  mb-5" >
                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_rank">Name, position, and qualifications</label>
                      <input 
                      {...register(`personnel.${index}.name_position`)} placeholder="Name Position"
                      id="name_position" type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Rate/Annum</label>
                      <div>
                       <span className="absolute pl-1 pt-2">$</span>
                      <input 
                      {...register(`personnel.${index}.rate_per_annum`)} placeholder="" step="0.01"
                      id="rate_per_annum" type="number" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 pl-4 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                      </div>
                    </div>


                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Hours/Week</label>
                      <input 
                      {...register(`personnel.${index}.hours_per_week`)} placeholder="" step="0.01"
                      id="hours_per_week" type="number" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Calc. Rate</label>
                       <div>
                       <span className="absolute pl-1 pt-2">$</span>
                      <input {...register(`personnel.${index}.calc_rate`)} placeholder="" step="0.01"
                      id="calc_rate"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 pl-4 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" type="number"/>
                       </div>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Fringe Benefits (%)</label>
                      <input {...register(`personnel.${index}.fringe_benefit`)} 
                      id="fringe_benefit"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                       
                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Estimated Expenses</label>
                       <div>
                       <span className="absolute pl-1 pt-2">$</span>
                       <input {...register(`personnel.${index}.est_expenses`)} placeholder=""
                      id="est_expenses" step="0.01"
                      className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 pl-4 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" type="number"/>
                       </div>
                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="cost">$</label>
                       <div>
                        <span className="absolute pl-1 pt-2">$</span>
                          <input {...register(`personnel.${index}.cost`)} placeholder=""
                          id="cost" step="0.01"
                          className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 pl-4 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" type="number"/>
                       </div>
                    </div>

                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => remove(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>
                  
                  </div>
  );
};

export default PersonnelFieldsRow;