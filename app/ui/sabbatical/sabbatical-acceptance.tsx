'use client';
import { useForm, useFieldArray } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useState } from "react";
import QuillEditor from '@/components/ui/quill-editor'; // Import QuillEditor

type FormValues = {
  first_name:string;
  last_name:string;
  department: string;
  applicant_name: string;
  emp_signature: string;
  emp_location: string;
  emp_sign_date: string;
  administrative_appointment: string;
  laboratory_safty_supervision: string;
  graduate_student_supervision: string;
  plan_sabbatical_leave: string;
  request_for_leave: string;

};

export  default function Form({ initialData = {} }: { initialData?: Partial<FormValues> }) {
  const router = useRouter();
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const { register, control, handleSubmit, formState: { isSubmitting }, } = useForm<FormValues>({
    defaultValues: {
      ...initialData,
    },
  });

  const onSubmit = async (data: FormValues) => {

    setLoading(true);
    setMessage("Submitting...");
    window.scrollTo({ top: 0, behavior: 'smooth' });

    try {
      const response = await fetch('/api/sabatical/sabbatical-acceptance', {
        method: 'POST',
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      const res = await response.json();
      if (res.success) {
        setMessage("Submitted successfully!");
        router.push('/dashboard/sabbatical/view-pending-list?message=Request+Submitted+Successfuly!&status=success');
      } else {
        setMessage(res.error || 'Submission failed.');
      }
    } catch (err) {
      setMessage('Unexpected error occurred.');
    }
  };

  return (
 
    <div className='w-full max-w-full px-5 m-auto lg:w-11/12 '>

      {message && <div className="mb-4"><div className="bg-blue-100 text-blue-700 p-3 rounded">{message}</div></div>}
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 relative mb-32" >
      
        <div  className=" top-0 left-0 flex flex-col w-full min-w-0 p-4 break-words bg-white border-0   shadow-soft-xl rounded-2xl bg-clip-border h-auto opacity-100 visible">
          <div>
                <p className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500'>Acceptance of All Declarations</p>

                <p className='md:mt-3 leading-normal text-sm mb-2 ml-1 font-bold text-xs text-slate-700 /80'>By checking the boxes below, I hereby certify that, to the best of my knowledge, the information provided is true, accurate and complete.</p>

                  <div className="flex flex-wrap mt-4 -mx-3 ">
                      <div className="w-full max-w-full px-3 flex-0 sm:w-12/12 flex pb-2">
                          <input type="checkbox" id="request_for_leave" {...register("request_for_leave", { required: true })} value="Yes" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft  appearance-none ml-3 mr-4 border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                          <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="request_for_leave">STEP 1 - REQUEST FOR LEAVE OF ABSENCE</label>
                      </div>
                      <div className="w-full max-w-full px-3 flex-0 sm:w-12/12 flex pb-2">
                          <input type="checkbox" id="plan_sabbatical_leave" {...register("plan_sabbatical_leave", { required: true })} value="Yes" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft  appearance-none ml-3 mr-4 border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                          <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="plan_sabbatical_leave">STEP 2 - PLAN FOR SABBATICAL LEAVE</label>
                      </div>
                      <div className="w-full max-w-full px-3 flex-0 sm:w-12/12 flex pb-2">
                          <input type="checkbox" id="graduate_student_supervision" {...register("graduate_student_supervision", { required: true })} value="Yes" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft  appearance-none ml-3 mr-4 border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                          <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="graduate_student_supervision">STEP 3 -  GRADUATE STUDENT SUPERVISION DURING LEAVE OF ABSENCE</label>
                      </div>
                      <div className="w-full max-w-full px-3 flex-0 sm:w-12/12 flex pb-2">
                          <input type="checkbox" id="laboratory_safty_supervision" {...register("laboratory_safty_supervision", { required: true })} value="Yes" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft  appearance-none ml-3 mr-4 border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                          <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="laboratory_safty_supervision">STEP 4 -  LABORATORY SAFETY SUPERVISION DURING LEAVE OF ABSENCE</label>
                      </div>
                      <div className="w-full max-w-full px-3 flex-0 sm:w-12/12 flex pb-2">
                          <input type="checkbox" id="administrative_appointment" {...register("administrative_appointment", { required: true })} value="Yes" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft  appearance-none ml-3 mr-4 border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                          <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="administrative_appointment">STEP 5 -  ADMINISTRATIVE APPOINTMENT DURING LEAVE OF ABSENCE</label>
                      </div>
                  </div>
                
                  <div className="flex flex-wrap mt-4 -mx-3">
                    <div className="w-full max-w-full px-3 flex-0 mb-5 sm:w-10/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="emp_signature">Enter Your Full Name as Electronic Signature</label>
                      <input type="text" {...register("emp_signature", { required: true })} defaultValue={initialData.emp_signature} placeholder="Enter Your Full Name as Electronic Signature" id="emp_signature" name="emp_signature" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                                
                    </div>
                    <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-5/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="emp_location">Location</label>
                      <input type="text" id="emp_location" {...register("emp_location", { required: true })}  className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>
                    <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-5/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="emp_sign_date">Today Date</label>
                      <input type="date" id="emp_sign_date" {...register("emp_sign_date", { required: true })}  value={new Date().toISOString().split('T')[0]} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" readOnly/>
                    </div>
                  </div>
                  
                  <div className="flex justify-end mt-6">
                    <button
                        type="submit"
                        disabled={loading}
                        className={`inline-block px-6 py-3 m-0 ml-2 text-xs font-bold text-center text-white uppercase align-middle transition-all border-0 rounded-lg cursor-pointer bg-150 bg-x-25   ${
                          loading ? 'bg-gray-400 cursor-not-allowed' : 'ease-soft-in leading-pro tracking-tight-soft bg-gradient-to-tl from-purple-700 to-pink-500 shadow-soft-md hover:scale-102 active:opacity-85'
                        } text-white`}
                      >
                      {loading ? '   Processing...   ' : 'Submit'}
                    </button>

                </div>
            </div>
      </div>

  </form>
  
  </div>

  );
}
        
      
