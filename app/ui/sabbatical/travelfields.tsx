import { useFormContext, useWatch, Controller } from "react-hook-form";
import { useEffect } from "react";

const TravelFieldsRow = ({ index, remove }: { index: number; remove: (index: number) => void }) => {
  const { control, register, setValue } = useFormContext();

  
  const unitCost = useWatch({ control, name: `travel.${index}.related_cost` });

  const calculatedCost =  (Number(unitCost) || 0);

  useEffect(() => {
    setValue(`travel.${index}.cost`, calculatedCost.toFixed(2));
  }, [unitCost, index, setValue]);

  return (
                <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300  mb-5" >
                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="first_rank">Location(s)</label>
                      <input 
                      {...register(`travel.${index}.location`)} placeholder=""
                      id="location"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-3/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="duration">Duration</label>
                      <input 
                      {...register(`travel.${index}.duration`)} placeholder=""
                      id="duration"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>


                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="travel_mode">Travel Mode</label>
                      <input 
                      {...register(`travel.${index}.travel_mode`)} placeholder=""
                      id="travel_mode"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="related_cost">Related Costs</label>
                       <div>
                        <span className="absolute pl-1 pt-2">$</span>
                        <input {...register(`travel.${index}.related_cost`)} placeholder=""
                        id="related_cost"
                        className="focus:shadow-soft-primary-outline /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 pl-4 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" type="number"/>
                       </div>
                    </div>


                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="cost">$</label>
                        <div>
                          <span className="absolute pl-1 pt-2">$</span>
                          <input {...register(`travel.${index}.cost`)} placeholder="" id="cost"
                          className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 pl-4 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" type="number"/>
                        </div>
                    </div>

                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => remove(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>
                  </div>
  );
};

export default TravelFieldsRow;