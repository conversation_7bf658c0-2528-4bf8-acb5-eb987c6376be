'use client';

import { useForm, useFieldArray } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import QuillEditor from '@/components/ui/quill-editor'; // Import QuillEditor

type FormValues = {

  leave_values : string;
  hold_funding: string;
  era_funding: string;
  era_supervision: string;
  out_rsrch_funding: string;
  decl_out_emply: string;
  pl_report?: FileList;

};


export default function Form({ initialData = {} }: { initialData?: Partial<FormValues> }) {

  const router = useRouter();
  const [error, setError] = useState("");
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  
  const { register, control, handleSubmit } = useForm<FormValues>({
    defaultValues: {
      ...initialData,
    },
  });

  const onSubmit = async (data: FormValues) => {

    setLoading(true);
    setMessage("Submitting...");
    window.scrollTo({ top: 0, behavior: 'smooth' });

    const formData = new FormData();
    formData.append('leave_values', data.leave_values);
    formData.append('hold_funding', data.hold_funding);
    formData.append('era_funding', data.era_funding);
    formData.append('era_supervision', data.era_supervision);
    formData.append('out_rsrch_funding', data.out_rsrch_funding);
    formData.append('decl_out_emply', data.decl_out_emply);

    // Add file to the form data (assuming 'file' is the name of the input field)
    if (data.pl_report && data.pl_report.length > 0) {
      formData.append('pl_report', data.pl_report[0]);
    }
    
    try {
      const response = await fetch('/api/sabbaticalForm/sabbatical-step2', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const res = await response.json();
        if (res.success) {
          setMessage("Submitted successfully!");
          router.push('/dashboard/sabbatical/sabbatical-step3?message=Request+submitted+Complete+Step+3&status=success');
        } else {
          setMessage(res.error || 'Submission failed.');
          //setError(res.error || 'Submission failed.');
        }
      }

    } catch (err) {
      setMessage('Unexpected error occurred.');
    }
    
  };

  return (

    <div className='w-full max-w-full px-5 m-auto lg:w-11/12 '>

      {message && <div className="mb-4"><div className="bg-blue-100 text-blue-700 p-3 rounded">{message}</div></div>}

    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 relative mb-32" >
    {error && <p className="text-red-500">{error}</p>}
      <div  className=" top-0 left-0 flex flex-col w-full min-w-0 p-4 pl-8 pr-8 pb-8 break-words bg-white border-0   shadow-soft-xl rounded-2xl bg-clip-border h-auto opacity-100 visible">

        <div>

        <h5 className="mb-0  font-semibold text-xl z-10 text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500">Planned scholarly activities</h5>

        <p className='md:mt-3 leading-normal text-sm'>Please provide the following information as part of the description of your scholarly activities:</p>
        <p className='md:mt-3 leading-normal text-sm'>
                    <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                    <li className='leading-normal text-sm'>Describe the value of your leave to the institution and to your professional development.</li>
                      <li>Describe who is involved in the research you propose to carry out.</li>
                      <li>Describe your research project(s), including how it/they fit/s into your longer-term research plans.</li>
                      <li>State when you will be travelling and your destinations.</li>
                    </ul>
                    </p>
        <div className="flex flex-wrap mt-4 -mx-3 pt-4">
          <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">


                  <QuillEditor
                    name="leave_values"
                    control={control}
                    placeholder="Describe the value of your leave to the institution and to your professional development."
                    defaultValue={initialData.leave_values || ''}
                  />

 
            </div>
        </div>

        <h5 className="mt-4 mb-0 font-semibold text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500">Declaration of Research Funding (Research Office will be notified prior to Provost approval):</h5>

        <div className="flex flex-wrap mt-4 -mx-3">
          <div className="w-full max-w-full mb-3 px-3 flex-0 sm:w-12/12">

              <input type="radio" id="yes_hold_funding" {...register("hold_funding")} value="yes" placeholder="eg. Prior" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
              <label className="mb-2 ml-3 text-sm text-slate-700" htmlFor="yes_hold_funding">Yes, I hold/will hold active or applied for research funding (internal or external).</label>
          </div>
          <div className="w-full max-w-full px-3 mb-3 mt-4 flex-0 sm:mt-0 sm:w-12/12">
              <input type="radio" id="no_hold_funding" {...register("hold_funding")}  value="no" placeholder="eg. Prior" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
              <label className="mb-2 ml-3 text-sm text-slate-700" htmlFor="no_hold_funding">No, I do not hold research funding.</label>

          </div>
        </div>
        
        <h5 className="mt-4 mb-0 font-semibold">If yes, will all active Tri-Agency and/or Early Research Award (ERA) funded research projects continue during the leave?</h5>

        <div className="flex flex-wrap -mx-3" >
          <div className=" mb-3 px-3 flex-0 mt-4">
                <input type="radio" id="Yes" {...register("era_funding")}  value="yes" placeholder="eg. Prior" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                <label className="mb-2 ml-3 text-sm text-slate-700" htmlFor="Yes">Yes</label>
            </div>
            <div className=" px-3 mb-3  mt-4 flex-0 ">
                <input type="radio" id="No" {...register("era_funding")} value="No" placeholder="eg. Prior" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                <label className="mb-2 ml-3 text-sm text-slate-700" htmlFor="No"> No</label>
            </div>
            <div className=" px-3 mb-3  mt-4 flex-0 ">
                <input type="radio" id="NA" {...register("era_funding")} value="NA" placeholder="eg. Prior" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                <label className="mb-2 ml-3 text-sm text-slate-700" htmlFor="NA">N/A</label>
            </div>
            
        </div>

        <h5 className="mt-4 mb-0 font-semibold">If yes, is supervision in place for all students? </h5>

        <div className="flex flex-wrap -mx-3">
          <div className=" mb-3 px-3 mt-4 flex-0 ">
                <input type="radio" id="era_Yes" {...register("era_supervision")}  value="yes" placeholder="eg. Prior" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                <label className="mb-2 ml-3 text-sm text-slate-700" htmlFor="era_Yes">Yes</label>
            </div>
            <div className=" px-3 mb-3  mt-4 flex-0 ">
                <input type="radio" id="era_No" {...register("era_supervision")} value="No" placeholder="eg. Prior" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                <label className="mb-2 ml-3 text-sm text-slate-700" htmlFor="era_No"> No</label>
            </div>
            <div className=" px-3 mb-3  mt-4 flex-0 ">
                <input type="radio" id="era_NA" {...register("era_supervision")} value="NA" placeholder="eg. Prior" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                <label className="mb-2 ml-3 text-sm text-slate-700" htmlFor="era_NA">N/A (no students) </label>
            </div>
            
        </div>


        <h5 className="mt-4 mb-0 font-semibold text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500">Declaration of Outside Employment or Fellowships</h5>

        <div className="flex flex-wrap mt-4 -mx-3">
          <div className=" mb-3 px-3 flex-0 w-full ">
                <input type="radio" id="out_rsrch_funding_yes" {...register("out_rsrch_funding")} value="yes" placeholder="eg. Prior" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                <label className="mb-2 ml-3 text-sm text-slate-700" htmlFor="out_rsrch_funding_yes">I will not be receiving any funding from outside employment or fellowships during my sabbatical</label>
            </div>
            <div className=" px-3 mb-3  mt-4 flex-0 sm:mt-0 w-full">
                <input type="radio" id="out_rsrch_funding_no" {...register("out_rsrch_funding")} value="No" placeholder="eg. Prior" className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                <label className="mb-2 ml-3 text-sm text-slate-700" htmlFor="out_rsrch_funding_no"> I will receive outside funding (please provide a brief explanation)</label>
            </div>
            
            
              <div className="w-full max-w-full px-3 flex-0 pt-4 sm:w-12/12">
              <QuillEditor
                    name="decl_out_emply"
                    control={control}
                    placeholder="Declaration of Outside Employment or Fellowships"
                    defaultValue={initialData.decl_out_emply || ''}
                  />
 
                </div>
            
        </div>
        <div className="flex flex-wrap mt-4 -mx-3">
        <p className='md:mt-3 leading-normal text-sm'>If you have held previous sabbatical leaves, please attach a report (2 pages max.) that provides the following information about your most recent sabbatical leave:</p>
        <p className='md:mt-3 leading-normal text-sm'>
                    <ul className='float-left pl-6 mb-0 list-disc text-slate-500 w-full'>
                    <li className='leading-normal text-sm'>How did your sabbatical research contribute to the institution and to your professional development?</li>
                      <li>Who was involved in the research you carried out?</li>
                      <li>Describe your research project(s), including how it/they aligned with or contributed to your longer-term research plans.</li>
                      <li>When and where did you travel during your sabbatical and what was the purpose of your travel?</li>
                      <li>If applicable, which grants did you apply for in preparation of and/or as a result of your sabbatical research and have these grants been awarded. Please provide details.</li>
                      <li>List the publications, conference presentations and any other forms of scholarly output that resulted from your sabbatical.</li>
                    </ul>
         </p>
        </div>

        <div className="flex flex-wrap mt-4 -mx-3">
          <div className=" mb-3 px-3 flex-0 ">

                <input type='file' id="pl_report" {...register("pl_report")} className="focus:shadow-soft-primary-outline text-sm leading-5.6 ease-soft appearance-none  bg-white bg-clip-padding font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" />
          </div>

        </div>

        <div className="flex justify-end mt-6">
          <button
                        type="submit"
                        disabled={loading}
                        className={`inline-block px-6 py-3 m-0 ml-2 text-xs font-bold text-center text-white uppercase align-middle transition-all border-0 rounded-lg cursor-pointer bg-150 bg-x-25   ${
                          loading ? 'bg-gray-400 cursor-not-allowed' : 'ease-soft-in leading-pro tracking-tight-soft bg-gradient-to-tl from-purple-700 to-pink-500 shadow-soft-md hover:scale-102 active:opacity-85'
                        } text-white`}
                      >
                      {loading ? '   Processing...   ' : 'Save and Continue'}
                    </button>
        </div>
      </div>
    </div>

  </form>
  
  </div>

  );
}
        
      
