'use client';

import { useForm, useField<PERSON>rray, FormProvider, useWatch, useFormContext } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useState, useEffect,  } from "react";
import QuillEditor from '@/components/ui/quill-editor';
import OtherExpenseRow from '@/app/ui/sabbatical/otherexpenses';
import SupplyFieldsRow from '@/app/ui/sabbatical/suppliesfields';
import EquipmentsFieldsRow from '@/app/ui/sabbatical/equipmentsfields';
import TravelFieldsRow from '@/app/ui/sabbatical/travelfields';
import PersonnelFieldsRow from '@/app/ui/sabbatical/personnelfields'; 

type PersonnelEntry = { name_position: string; rate_per_annum: string; hours_per_week: string; calc_rate: string; fringe_benefit: string; est_expenses: string; cost: string; };
type TravelEntry = { location: string; duration: string; travel_mode: string; related_cost: string; cost: string; };
type OtherEntry = { quantity: string; description: string; unit_cost: string; cost: string; };

type FormValues = {
  appl_id: string;
  applicant_name: string;
  department: string;
  date: string;
  no_of_month: string;
  starting_term: string;
  ending_term: string;
  project_title: string;
  research_location: string;
  amount_requested: string;
  research_description: string;
  personnel: PersonnelEntry[];
  travel: TravelEntry[];
  equipment: OtherEntry[];
  supplies: OtherEntry[];
  otherexpenses: OtherEntry[];
  totalfundrequested: string;
};

export default function Form({ initialData = {} }: { initialData?: Partial<FormValues> }) {

  const router = useRouter();
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const methods = useForm<FormValues>({
    defaultValues: {
      ...initialData,
      personnel: [{ name_position: "", rate_per_annum: "", hours_per_week: "", calc_rate: "", fringe_benefit: "", est_expenses: "", cost: "" }],
      travel: [{ location: "", duration: "", travel_mode: "", related_cost: "", cost: "" }],
      equipment: [{ quantity: "", description: "", unit_cost: "", cost: "" }],
      supplies: [{ quantity: "", description: "", unit_cost: "", cost: "" }],
      otherexpenses: [{ quantity: "", description: "", unit_cost: "", cost: "" }],
      totalfundrequested: "0",
    },
  });

  const { control, register, setValue } = methods;
  const { fields: personnelFields, append: appendPersonnel, remove: removePersonnel } = useFieldArray({ control, name: "personnel" });
  const { fields: travelFields, append: appendTravel, remove: removeTravel } = useFieldArray({ control, name: "travel" });
  const { fields: equipmentFields, append: appendEquipment, remove: removeEquipment } = useFieldArray({ control, name: "equipment" });
  const { fields: suppliesFields, append: appendSupplies, remove: removeSupplies } = useFieldArray({ control, name: "supplies" });
  const { fields: otherexpensesFields, append: appendOtherExpenses, remove: removeOtherExpenses } = useFieldArray({ control, name: "otherexpenses" });

  const personnelCosts = useWatch({control, name: "personnel",});
  const travelCosts = useWatch({control, name: "travel",});
  const equipmentCosts = useWatch({control, name: "equipment",});
  const suppliesCosts = useWatch({control, name: "supplies",});
  const otherExpCosts = useWatch({control, name: "otherexpenses",});

useEffect(() => {
  const total = [personnelCosts, travelCosts, equipmentCosts, suppliesCosts, otherExpCosts].reduce((sum, section) => {
    if (!section || !Array.isArray(section)) return sum;
    const sectionTotal = section.reduce((acc, item) => acc + parseFloat(item?.cost || "0"), 0);
    return sum + sectionTotal;
  }, 0);

  setValue("totalfundrequested", total.toFixed(2));
}, [personnelCosts, travelCosts, equipmentCosts, suppliesCosts, otherExpCosts, setValue]);

  const onSubmit = async (data: FormValues) => {
    setLoading(true);
    setMessage("Submitting...");
    window.scrollTo({ top: 0, behavior: 'smooth' });

    try {
      const response = await fetch('/api/sabatical/sabbatical-step6', {
        method: 'POST',
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      const res = await response.json();
      if (res.success) {
        setMessage("Submitted successfully!");
        router.push('/dashboard/sabbatical/sabbatical-show-acceptance?message=Request+submitted&status=success');
      } else {
        setMessage(res.error || 'Submission failed.');
      }
    } catch (err) {
      setMessage('Unexpected error occurred.');
     // setError('Unexpected error occurred.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full max-w-full px-5 m-auto lg:w-11/12">
      
     {message && <div className="mb-4"><div className="bg-blue-100 text-blue-700 p-3 rounded">{message}</div></div>}
      
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6 relative mb-32">

        
        <div  className=" top-0 left-0 flex flex-col w-full min-w-0 p-4 break-words bg-white border-0   shadow-soft-xl rounded-2xl bg-clip-border h-auto opacity-100 visible">

          <div>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="delg_supervisor">Applicant Name</label>
                    <input type="text" {...register("applicant_name", { required: false })} defaultValue={initialData.applicant_name} placeholder="Name" id="applicant_name" name="applicant_name" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" readOnly/>
                              
                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Department</label>
                    <input type="text" {...register("department", { required: false })} defaultValue={initialData.department} placeholder="dept_name" id="dept_name" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" readOnly/>`

                  </div>
                  <div className="w-full max-w-full px-3 mt-4 flex-0 sm:mt-0 sm:w-4/12">
                    <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Date</label>
                    <input type="date" id="date" {...register("date", { required: true })}  value={new Date().toISOString().split('T')[0]} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" readOnly/>

                  </div>
                </div>
                <h2 className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500 '>A. BASIC INFORMATION</h2>

                <p className='md:mt-3 leading-normal text-sm mb-2 ml-1 font-bold text-xs text-slate-700 /80'>1.	Indicate the Term of Research Leave Granted:: </p>

                  <div className="flex flex-wrap mt-2 -mx-3 " >
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-4/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Starting Term</label>
                      
                       <select {...register(`starting_term`)} className="focus:shadow-soft-primary-outline /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                        <option key="starting_term" value="">Select Term</option>
                           <option value="Spring">Spring</option>
                           <option value="Fall">Fall</option>
                           <option value="Winter">Winter</option>
                      </select>

                    </div>
                    <div className="w-full max-w-full px-3  flex-0 sm:mt-0 sm:w-4/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Ending Term</label>
                      <select {...register(`ending_term`)} className="focus:shadow-soft-primary-outline /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">
                        <option key="ending_term" value="">Select Term</option>
                           <option value="Spring">Spring</option>
                           <option value="Fall">Fall</option>
                           <option value="Winter">Winter</option>
                      </select>
                      
                    
                    </div>
                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-4/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="dept_head">Number of Months</label>
                      <input {...register(`no_of_month`)} placeholder="Number of Months" type="text"
                      id="no_of_month" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                       
                    </div>
                  </div>

                  <p className='md:mt-3 leading-normal mb-2 ml-1 font-bold text-xs text-slate-700 /80'>2.	Title of Research Project:</p>
                
                  <div className="flex flex-wrap mt-4 -mx-3">
                      <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                        <input type="text" {...register("project_title", { required: false })} defaultValue={initialData.project_title} placeholder="" id="project_title" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`
                      </div>
                  </div>

                  <p className='md:mt-3 leading-normal mb-2 ml-1 font-bold text-xs text-slate-700 /80'>3.	Location of Research (Please name principal building or site where the research will be performed, for example, a college, hospital, main campus, or site in the Arctic):</p>
                
                  <div className="flex flex-wrap mt-4 -mx-3">
                      <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                        <input type="text" {...register("research_location", { required: false })} defaultValue={initialData.research_location} placeholder="" id="research_location" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`
                      </div>
                  </div>


                  <p className='md:mt-3 leading-normal mb-2 ml-1 font-bold text-xs text-slate-700 /80'>4.	Total Amount Requested:</p>
                
                  <div className="flex flex-wrap mt-4 -mx-3">
                      <div className="w-full max-w-full px-3 flex-0 sm:w-3/12 ">
                        <span className="absolute pl-2 pt-2">$</span>

                        <input type="number" {...register("amount_requested", { required: false })} defaultValue={initialData.amount_requested} placeholder="" id="amount_requested" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 pl-6 pr-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`
  
                      </div>
                      <div className="w-full max-w-full px-3 flex-0 sm:w-12/12 text-xs text-slate-500">
                      Please note that T4A slips will be issued for the taxation year in which the installment payment is received, not the date on which the Payroll department receives the Research Grant Payment Form. (Installments will be  included  in  the  normal  salary  payments).  Visit  Revenue  Canada <a href="http://www.cra-arc.gc.ca/tx/tchncl/ncmtx/fls/s1/f2/s1-f2-c3-eng.html#N108BF">http://www.cra-arc.gc.ca/tx/tchncl/ncmtx/fls/s1/f2/s1-f2-c3-eng.html#N108BF</a> for further information.
                      </div>
                  </div>


                <p className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500'>B.	DESCRIPTION OF PROPOSED RESEARCH</p>
                <div className="flex flex-wrap mt-4 -mx-3">
                  <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                    <label className="font-bold text-xs text-slate-700 /80" htmlFor="research_description">Please give a description of the research objectives and procedures and a justification of the budget items listed under Section C and the choice of location(s), if any. Please note that the purpose and objects of the expenditures proposed must be warranted in the context of the research outlined. Applications that do not provide sufficient information will be returned.</label>
                    <QuillEditor
                      name="research_description"
                      control={control}
                      placeholder="The following arrangements have been made for my Administrative Appointment(s)"
                      defaultValue={initialData.research_description || ''}
                    />
                  </div>
                </div>
                <p className='md:mt-3 leading-normal text-transparent bg-clip-text bg-gradient-to-tl from-purple-700 to-pink-500'>C.	BUDGET</p>
                
                  <div className="flex flex-wrap mt-4 -mx-3">
                    <div className="w-full max-w-full px-3 flex-0 sm:w-12/12">
                          <label className="font-bold text-xs text-slate-700 /80" htmlFor="research_description">Please give a description of the research objectives and procedures and a justification of the budget items listed under Section C and the choice of location(s), if any. Please note that the purpose and objects of the expenditures proposed must be warranted in the context of the research outlined. Applications that do not provide sufficient information will be returned.</label>
                        <input type="text" {...register("amount_requested", { required: false })} defaultValue={initialData.amount_requested} placeholder="" id="amount_requested" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>`

                    </div>
                   
                  </div>

                  <p className='md:mt-3 leading-normal text-sm font-semibold'>PERSONNEL: <button type="button" onClick={() => appendPersonnel({ name_position: "",  rate_per_annum: "",  hours_per_week: "",  calc_rate: "",  fringe_benefit: "",  est_expenses: "",  cost: "" })} className="float-right"> + Add Entry
                </button></p>
                 
                {personnelFields.map((field, index) => (
                  <PersonnelFieldsRow key={field.id} index={index} remove={removePersonnel} />
                ))}

                <p className='md:mt-3 leading-normal text-sm '><b>TRAVEL AND RELATED COSTS FOR PRINCIPAL INVESTIGATOR</b> essential to research program (expense for sojourning and for spouse and family are not allowable) &nbsp;&nbsp;<button type="button" onClick={() => appendTravel({ location: "", duration: "", travel_mode: "", related_cost: "", cost: "" })} className="float-right"> + Add Entry
                </button></p>

                {travelFields.map((field, index) => (
                  <TravelFieldsRow key={field.id} index={index} remove={removeTravel} />
                ))}

                <p className='md:mt-3 leading-normal text-sm '><b>EQUIPMENT </b> (list specific items) &nbsp;&nbsp;<button type="button" onClick={() => appendEquipment({ quantity: "", description: "", unit_cost: "", cost: "" })} className="float-right"> + Add Entry
                </button></p>

                {equipmentFields.map((field, index) => (
                  <EquipmentsFieldsRow key={field.id} index={index} remove={removeEquipment} />

                ))}

                <p className='md:mt-3 leading-normal text-sm '><b>SUPPLIES </b> (list specific items) &nbsp;&nbsp;<button type="button" onClick={() => appendSupplies({ quantity: "", description: "", unit_cost: "", cost: "" })} className="float-right"> + Add Entry
                </button></p>

                {suppliesFields.map((field, index) => (
                  <SupplyFieldsRow key={field.id} index={index} remove={removeSupplies} />
                ))}

                <p className='md:mt-3 leading-normal text-sm '><b>OTHER EXPENSES </b> (be specific) &nbsp;&nbsp;<button type="button" onClick={() => appendOtherExpenses({ quantity: "", description: "", unit_cost: "", cost: "" })} className="float-right"> + Add Entry
                </button></p>

                {otherexpensesFields.map((field, index) => (
                  <OtherExpenseRow key={field.id} index={index} remove={removeOtherExpenses} />
                ))}

                  <div className="flex flex-wrap mt-4 -mx-3">
                    <div className="w-full max-w-full px-3 flex-0 sm:w-3/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="totalfundrequested">Total Fund Requested</label>
                      <div>
                          <span className="absolute pl-2 pt-2">$</span>
                          <input 
                          {...register(`totalfundrequested`)} placeholder=""
                          id="totalfundrequested" step="0.01"
                          type="number" inputMode="numeric" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 pl-6 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none "/>
                          </div>
                    </div>
                   
                  </div>

                  <div className="flex justify-end mt-6 ">

                     <button
                        type="submit"
                        disabled={loading}
                        className={`inline-block px-6 py-3 m-0 ml-2 text-xs font-bold text-center text-white uppercase align-middle transition-all border-0 rounded-lg cursor-pointer bg-150 bg-x-25   ${
                          loading ? 'bg-gray-400 cursor-not-allowed' : 'ease-soft-in leading-pro tracking-tight-soft bg-gradient-to-tl from-purple-700 to-pink-500 shadow-soft-md hover:scale-102 active:opacity-85'
                        } text-white`}
                      >
                      {loading ? '   Processing...   ' : 'Save and Continue'}
                    </button>
                    
                  </div>
            </div>
      </div>

  </form>
  </FormProvider>
  </div>


  );
}
        
      
