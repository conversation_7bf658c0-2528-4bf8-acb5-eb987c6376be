import { useFormContext, useWatch, Controller } from "react-hook-form";
import { useEffect } from "react";

const EquipmentsFieldsRow = ({ index, remove }: { index: number; remove: (index: number) => void }) => {
  const { control, register, setValue } = useFormContext();

  const quantity = useWatch({ control, name: `equipment.${index}.quantity` });
  const unitCost = useWatch({ control, name: `equipment.${index}.unit_cost` });

  const calculatedCost = (Number(quantity) || 0) * (Number(unitCost) || 0);

  useEffect(() => {
    setValue(`equipment.${index}.cost`, calculatedCost.toFixed(2));
  }, [quantity, unitCost, index, setValue]);

  return (
                <div className="flex flex-wrap mt-2 -mx-3 border-b border-solid border-gray-300  mb-5">
                    <div className="w-full max-w-full px-3 flex-0 sm:w-2/12 ">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="quantity">Quantity</label>
                      <input 
                      {...register(`equipment.${index}.quantity`)} placeholder=""
                      id="quantity" type="number" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-6/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="description">Description</label>
                      <input 
                      {...register(`equipment.${index}.description`)} placeholder=""
                      id="description"
                      type="text" className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none"/>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-2/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="unit_cost">Unit Cost</label>
                      <div>
                          <span className="absolute pl-1 pt-2">$</span>
                            <input {...register(`equipment.${index}.unit_cost`)} placeholder=""
                            id="unit_cost" step="0.01"
                            className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 pl-4  py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" type="number"/>
                      </div>
                    </div>

                    <div className="w-full max-w-full px-3 flex-0 sm:mt-0 sm:w-1/12">
                      <label className="mb-2 ml-1 font-bold text-xs text-slate-700 /80" htmlFor="cost">$</label>
                      <div>
                          <span className="absolute pl-1 pt-2">$</span>
                          <input {...register(`equipment.${index}.cost`)} placeholder=""
                          id="cost" step="0.01"
                          className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 pl-4 py-2 w-full font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none" type="number"/>
                          </div>
                    </div>

                    <div className="p-4 rounded  sm:w-1/12">
                      <button type="button" onClick={() => remove(index)} className="focus:shadow-soft-primary-outline   /80 text-sm leading-5.6 ease-soft block  appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding mt-2 mr-2 px-3 py-2 font-normal text-gray-700 outline-none transition-all placeholder:text-gray-500 focus:border-fuchsia-300 focus:outline-none">X</button>
                    </div>
                  </div>
  );
};

export default EquipmentsFieldsRow;