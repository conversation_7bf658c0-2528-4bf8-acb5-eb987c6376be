import { getServerSession } from "next-auth/next";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "@/app/lib/auth";
import { sql } from "@/app/lib/db";
import { forbidden, handleApiError } from "@/lib/error-handler";


export async function GET(req: NextRequest) {

  const session = await getServerSession(authOptions);
  
  if (!session?.user?.roles?.includes("system_admin") && !session?.user?.roles?.includes("faculty_admin")) {
    throw forbidden("You don't have permission to access.");
  }

  const { searchParams } = new URL(req.url);
  const userRequest = searchParams.get("userRequest");

  if (!userRequest) {
      return NextResponse.json({ error: "ID is required" }, { status: 400 });
  }
  await sql `SET search_path TO uw`;

  try {

      const result = await sql`
      SELECT *,
      sr.first_name || ' ' || sr.last_name AS name
      FROM uw.sabbatical_requests sr
      LEFT JOIN uw.sabbatical_details sbdt 
          ON sbdt.appl_id = sr.appl_id AND sr.deleted_at IS NULL
      LEFT JOIN uw.sabbatical_student_supervision sbssup 
          ON sbssup.appl_id = sr.appl_id AND sr.deleted_at IS NULL
      LEFT JOIN uw.subbatical_laboratory_supervision sblabsup 
          ON sblabsup.appl_id = sr.appl_id AND sr.deleted_at IS NULL
      LEFT JOIN uw.subbatical_administrative_appointment sbadappt 
          ON sbadappt.appl_id = sr.appl_id AND sr.deleted_at IS NULL
      LEFT JOIN uw.sabatical_research_grant sbresgrt 
          ON sbresgrt.appl_id = sr.appl_id AND sr.deleted_at IS NULL
      WHERE sr.appl_id = ${userRequest}
    `;

    return NextResponse.json({ data: result}, { status: 200 });

  } catch (error) {

    const { error: errorMessage, status } = handleApiError(error);
    return NextResponse.json({ error: errorMessage }, { status });

  }

}