// app/api/sabbatical/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from "next-auth/next";
import { authOptions } from '@/app/lib/auth';
import { sql } from "@/app/lib/db";

export async function GET(request: Request) {
  try {

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return new Response(
        JSON.stringify({ success: false, error: 'Missing id parameter' }),
        { status: 400 }
      );
    }

    await sql `SET search_path TO engrecords`;

    const engPastLeaves = await sql`
      SELECT leave_type as "type", leave_eff_from_dt as "from", leave_eff_to_dt as "to", leave_salary_perc as "salaryArrangement"
      FROM engrecords.leaves WHERE fac_nexus = ${id}
    `;

    await sql `SET search_path TO uw`;

    const uwPastLeaves = await sql`
      SELECT leave_type as "type", leave_eff_from_dt as "from", leave_eff_to_dt as "to", leave_salary_perc as "salaryArrangement"
      FROM uw.leaves WHERE facultyssoid = ${id}
    `;

    const combined = [...engPastLeaves, ...uwPastLeaves];

    return new Response(
      JSON.stringify({ success: true, data: combined }),
      { status: 200 }
    );

    
  } catch (error) {

    const message = error instanceof Error ? error.message : 'Unknown error';
    return new Response(
      JSON.stringify({ success: false, error: message }),
      { status: 500 }
    );


  }
}

